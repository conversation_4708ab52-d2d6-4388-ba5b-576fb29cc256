/* <PERSON><PERSON> Poster Maker - Borderless Print Styles */
/* 無邊框打印樣式 */

:root {
  --color-1: rgb(0, 200, 255);
  --color-2: rgb(255, 200, 0);
  --color-3: rgb(47, 255, 92);
  --color-4: rgb(255, 49, 49);
  --color-5: rgb(0, 255, 234);
  --border-color-1: rgb(0, 162, 255);
  --border-color-2: rgb(255, 180, 19);
  --border-color-3: rgb(30, 255, 0);
  --border-color-4: rgb(255, 63, 63);
  --border-color-5: rgb(12, 255, 243);
  --text-color-1: rgb(0, 0, 0);
  --text-color-2: rgb(255, 255, 255);
  --text-color-3: rgb(0, 255, 128);
  --text-color-4: rgb(255, 128, 0);
  --text-color-5: rgb(255, 0, 128);
}

/* 基礎樣式 - 無邊框打印設置 */
@media print {
    /* 強制所有頁面邊距為零 */
    @page {
        margin: 0 !important;
        padding: 0 !important;
        size: auto !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        border: none !important;
        outline: none !important;
    }
    
    /* 針對不同頁面類型的邊距設置 */
    @page :first {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    @page :left {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    @page :right {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    @page :blank {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
    }
    
    /* Letter 尺寸特定設置 */
    @page letter {
        size: 8.5in 11in;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 4x6 尺寸特定設置 */
    @page photo {
        size: 4in 6in;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 基礎元素重置 */
    * {
        margin: 0 !important;
        padding: 0 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        box-sizing: border-box !important;
    }
    
    html {
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
    }
    
    body {
        margin: 0 !important;
        padding: 0 !important;
        background: transparent !important;
        width: 100% !important;
        height: 100% !important;
        overflow: visible !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* 布局樣式 - 打印佈局優化 */
@media print {
    /* 隱藏所有非打印元素 */
    body * {
        visibility: hidden !important;
    }
    
    /* 只顯示海報畫布 */
    .kms-poster-canvas,
    .kms-poster-canvas * {
        visibility: visible !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    /* 海報畫布絕對定位 */
    .kms-poster-canvas {
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        transform: none !important;
        page-break-inside: avoid !important;
        overflow: visible !important;
    }
    
    /* 預覽容器設置 */
    .preview-container {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: 100% !important;
        min-height: auto !important;
        display: block !important;
        position: static !important;
    }
}

/* 交互樣式 - 隱藏控制元素 */
@media print {
    /* 隱藏所有控制元素 */
    .kms-canvas-element.selected::after,
    .kms-resize-handle,
    .kms-canvas-tools,
    .kms-alignment-guide,
    .kms-position-indicator,
    .kms-canvas-grid,
    .print-controls {
        display: none !important;
        visibility: hidden !important;
    }
    
    /* 確保元素正確顯示 */
    .kms-canvas-element {
        position: absolute !important;
        max-width: none !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .kms-text-element {
        padding: inherit !important;
        line-height: inherit !important;
        word-wrap: break-word !important;
        white-space: pre-wrap !important;
        background-color: inherit !important;
        color: inherit !important;
        font-weight: inherit !important;
        font-style: inherit !important;
        text-decoration: inherit !important;
        text-align: inherit !important;
        border: inherit !important;
        border-radius: inherit !important;
        text-shadow: inherit !important;
        box-sizing: border-box !important;
        max-width: none !important;
        max-height: none !important;
        transform: none !important;
        zoom: 1 !important;
        scale: 1 !important;
    }
    
    .kms-image-element {
        max-width: none !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .kms-image-element img {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        max-width: none !important;
        max-height: none !important;
    }
    
    .kms-qr-element {
        max-width: none !important;
        max-height: none !important;
        overflow: visible !important;
    }
    
    .kms-qr-element img {
        display: block !important;
        width: 100% !important;
        height: auto !important;
        max-width: none !important;
        max-height: none !important;
    }
}

/* 響應式樣式 - 不同尺寸的打印設置 */
@media print {
    /* Letter 尺寸特定樣式 */
    .kms-canvas-letter {
        width: 8.5in !important;
        height: 11in !important;
        max-width: none !important;
        max-height: none !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
    }
    
    /* 4x6 尺寸特定樣式 */
    .kms-canvas-4x6 {
        width: 4in !important;
        height: 6in !important;
        max-width: none !important;
        max-height: none !important;
        position: absolute !important;
        left: 0 !important;
        top: 0 !important;
    }
}

/* 瀏覽器特定的無邊框打印修正 */
@media print {
    /* Chrome/Safari 特定修正 */
    @supports (-webkit-appearance: none) {
        @page {
            margin: 0 !important;
            -webkit-print-color-adjust: exact !important;
        }
        
        body {
            -webkit-print-color-adjust: exact !important;
        }
    }
    
    /* Firefox 特定修正 */
    @supports (-moz-appearance: none) {
        @page {
            margin: 0 !important;
        }
        
        body {
            margin: 0 !important;
            padding: 0 !important;
        }
    }
    
    /* Edge 特定修正 */
    @supports (-ms-ime-align: auto) {
        @page {
            margin: 0 !important;
        }
    }
}

/* 強制無邊框設置 - 最高優先級 */
@media print {
    html, body, .kms-poster-canvas {
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        outline: none !important;
    }
    
    /* 確保沒有任何邊距或填充 */
    * {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 移除所有可能的邊框和陰影 */
    .kms-poster-canvas {
        box-shadow: none !important;
        border: none !important;
        outline: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}