/**
 * KMS Poster Maker - Main Application Controller
 * 主應用程序控制器
 */

class KMSPosterMaker {
    constructor() {
        this.currentLanguage = 'en';
        this.currentPaperSize = 'letter';
        this.selectedElement = null;
        this.elements = [];
        this.elementCounter = 0;
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.layerManager = null;
        this.currentQuickColorTarget = 'text';
        this.translations = this.getTranslations();
        this.init();
    }

    init() {
        this.initializeComponents();
        this.setupEventListeners();
        this.setupCanvas();
        this.loadFonts();
        this.updateLanguage();
        
        // 確保頁面完全載入後執行縮放
        setTimeout(() => {
            this.handleCanvasResize();
            this.initializeZoomSlider();
        }, 100);
    }

    initializeComponents() {
        this.printHandler = new KMSPrintHandler(this);
        this.imageHandler = new KMSImageHandler(this);
        this.qrGenerator = new KMSQRGenerator(this);
        this.textHandler = new KMSTextHandler(this);
        this.initializeLayerManager();
        this.initializeQuickColors();
        this.initializeEnhancedControls();
    }

    initializeQuickColors() {
        // Wait for DOM to be fully loaded
        setTimeout(() => {
            document.querySelectorAll('.kms-color-target-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const target = e.currentTarget.dataset.target;
                    this.setQuickColorTarget(target);
                    document.querySelectorAll('.kms-color-target-btn').forEach(btn => btn.classList.remove('active'));
                    e.currentTarget.classList.add('active');
                });
            });

            document.querySelectorAll('.kms-quick-color-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const color = e.currentTarget.dataset.color;
                    if (this.selectedElement) {
                        this.applyQuickColor(color);
                        document.querySelectorAll('.kms-quick-color-btn').forEach(btn => btn.classList.remove('active'));
                        e.currentTarget.classList.add('active');
                    } else {
                        // 顯示提示訊息
                        this.showNotification(
                            this.currentLanguage === 'en' 
                                ? 'Please select an element first' 
                                : '請先選擇一個元素'
                        );
                    }
                });
            });

            // Set default target button as active
            const defaultTargetBtn = document.querySelector('.kms-color-target-btn[data-target="text"]');
            if (defaultTargetBtn) {
                defaultTargetBtn.classList.add('active');
            }

            this.updateQuickColorDisplay();
        }, 100);
    }

    setQuickColorTarget(target) {
        this.currentQuickColorTarget = target;
        this.updateQuickColorDisplay();
    }

    updateQuickColorDisplay() {
        const targetLabel = document.querySelector('.kms-current-target-label i');
        const targetName = document.querySelector('.kms-current-target-name');
        if (!targetLabel || !targetName) return;

        const targetConfig = {
            text: { icon: 'fas fa-font', key: 'textColor' },
            border: { icon: 'fas fa-border-style', key: 'textBorder' },
            background: { icon: 'fas fa-fill', key: 'textBackground' },
            shadow: { icon: 'fas fa-adjust', key: 'textShadow' },
            imageBorder: { icon: 'fas fa-image', key: 'imageBorder' }
        };

        const config = targetConfig[this.currentQuickColorTarget];
        if (config) {
            targetLabel.className = config.icon;
            const translations = this.getTranslations();
            if (translations[config.key]) {
                targetName.textContent = translations[config.key];
            }
        }
    }

    applyQuickColor(color) {
        if (!this.selectedElement) return;

        const target = this.currentQuickColorTarget;
        const colorMappings = {
            text: { id: 'fontColor', handler: 'updateColorPreview' },
            border: { id: 'borderColor', handler: 'updateSelectedTextBorder' },
            background: { id: 'textBgColor', handler: 'updateSelectedTextBackground' },
            shadow: { id: 'shadowColor', handler: 'updateTextShadow' }
        };

        if (this.selectedElement.classList.contains('kms-text-element') && colorMappings[target]) {
            const mapping = colorMappings[target];
            const colorPicker = document.getElementById(mapping.id);
            if (colorPicker) {
                colorPicker.value = color;
                if (this.textHandler) {
                    this.textHandler.updateColorPreview(colorPicker, color);
                    
                    // 處理不同的顏色應用邏輯
                    if (mapping.handler === 'updateSelectedTextBorder') {
                        this.textHandler.updateSelectedTextBorder('borderColor', color);
                    } else if (mapping.handler === 'updateSelectedTextBackground') {
                        this.textHandler.updateSelectedTextBackground();
                    } else if (mapping.handler === 'updateTextShadow') {
                        this.textHandler.updateTextShadow();
                    } else if (mapping.handler === 'updateColorPreview') {
                        // 直接更新文字顏色
                        this.textHandler.updateSelectedTextStyle('color', color);
                    }
                }
                this.updateColorValueDisplay(colorPicker, color);
            }
        } else if (this.selectedElement.classList.contains('kms-image-element') && target === 'imageBorder') {
            this.selectedElement.style.borderColor = color;
            const borderColorPicker = document.getElementById('imageBorderColor');
            if (borderColorPicker) {
                borderColorPicker.value = color;
                this.updateColorValueDisplay(borderColorPicker, color);
            }
        }
    }

    updateColorValueDisplay(colorPicker, color) {
        const colorRow = colorPicker.closest('.kms-enhanced-color-row');
        if (colorRow) {
            const colorValue = colorRow.querySelector('.kms-color-value');
            if (colorValue) {
                colorValue.textContent = color.toUpperCase();
            }
        }
    }

    showNotification(message) {
        // 創建通知元素
        const notification = document.createElement('div');
        notification.className = 'kms-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-4);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            font-weight: 500;
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        // 3秒後自動移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    setupEventListeners() {
        const eventMappings = [
            { id: 'languageToggle', event: 'click', handler: () => this.toggleLanguage() },
            { id: 'addTextBtn', event: 'click', handler: () => this.addTextElement() },
            { id: 'addImageBtn', event: 'click', handler: () => this.triggerImageUpload() },
            { id: 'addQRBtn', event: 'click', handler: () => this.showQRGenerator() },
            { id: 'printBtn', event: 'click', handler: () => this.printPoster() },
            { id: 'bgColor', event: 'change', handler: (e) => this.changeBackgroundColor(e.target.value) },

            // 新的畫布控制按鈕
            { id: 'toggleGridBtn', event: 'click', handler: () => this.toggleGrid() }
        ];

        eventMappings.forEach(({ id, event, handler }) => {
            const element = document.getElementById(id);
            if (element) element.addEventListener(event, handler);
        });

        document.querySelectorAll('.kms-paper-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.changePaperSize(e.currentTarget.dataset.size);
            });
        });

        document.querySelectorAll('.kms-canvas-border-option').forEach(option => {
            option.addEventListener('click', (e) => {
                this.changeCanvasBorder(e.currentTarget.dataset.border);
            });
        });

        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        }

        const paperRadius = document.getElementById('paperRadius');
        const paperRadiusValue = document.getElementById('paperRadiusValue');
        if (paperRadius && paperRadiusValue) {
            paperRadius.addEventListener('input', (e) => {
                const radius = e.target.value + 'px';
                paperRadiusValue.textContent = radius;
                this.changePaperRadius(radius);
            });
        }

        document.addEventListener('keydown', (e) => this.handleKeyDown(e));

        const canvasContainer = document.querySelector('.kms-canvas-container');
        if (canvasContainer) {
            canvasContainer.addEventListener('dragover', (e) => e.preventDefault());
            canvasContainer.addEventListener('drop', (e) => e.preventDefault());
        }

        // 縮放滑動條事件監聽器
        const zoomSlider = document.getElementById('zoomSlider');
        const zoomValue = document.getElementById('zoomValue');
        if (zoomSlider && zoomValue) {
            // 初始化滑動條值
            this.initializeZoomSlider();
            
            zoomSlider.addEventListener('input', (e) => {
                const zoomPercent = parseInt(e.target.value);
                const zoomScale = zoomPercent / 100;
                this.setCanvasZoom(zoomScale);
                zoomValue.textContent = zoomPercent + '%';
            });
        }
    }

    toggleLanguage() {
        this.currentLanguage = this.currentLanguage === 'en' ? 'zh' : 'en';
        this.updateLanguage();
    }

    getTranslations() {
        return {
            en: {
                title: 'KMS Poster Maker', paperSize: 'Paper Size', letter: 'Letter (8.5" × 11")',
                size4x6: '4" × 6"', addElements: 'Add Elements', addText: 'Add Text',
                addImage: 'Add Image', addQR: 'Add QR Code', background: 'Background',
                borders: 'Canvas Borders', print: 'Print Poster', textControls: 'Text Controls',
                imageControls: 'Image Controls', qrControls: 'QR Code Controls',
                font: 'Font', fontSize: 'Font Size', fontColor: 'Font Color',
                textBorder: 'Text Border', borderWidth: 'Border Width',
                borderColor: 'Border Color', borderRadius: 'Border Radius',
                enterUrl: 'Enter URL for QR Code', generate: 'Generate QR Code',
                uploadImage: 'Click to upload or drag image here',
                supportedFormats: 'Supported: JPG, PNG, GIF', textColor: 'Text Color',
                textBackground: 'Text Background', textShadow: 'Text Shadow',
                imageBorder: 'Image Border',
                // File Management translations
                savePoster: 'Save Poster', posterName: 'Poster Name', enterPosterName: 'Enter poster name',
                cancel: 'Cancel', save: 'Save', exportJSON: 'Export JSON', loadPoster: 'Load Poster',
                noPreview: 'No Preview', paper: 'Paper', elements: 'Elements', load: 'Load',
                importFromFile: 'Import from File', import: 'Import', manageSavedPosters: 'Manage Saved Posters',
                export: 'Export', delete: 'Delete', total: 'Total', posters: 'posters',
                clearAll: 'Clear All', close: 'Close', noSavedPosters: 'No Saved Posters',
                noSavedPostersMessage: 'You haven\'t saved any posters yet.',
                createPosterMessage: 'Create and save your first poster to get started!', ok: 'OK',
                // Canvas Controls translations
                canvasControls: 'Canvas Controls', toggleGrid: 'Toggle Grid',
                zoomIn: 'Zoom In', zoomOut: 'Zoom Out'
            },
            zh: {
                title: 'KMS 海報製作器', paperSize: '紙張尺寸', letter: 'Letter (8.5" × 11")',
                size4x6: '4" × 6"', addElements: '添加元素', addText: '添加文字',
                addImage: '添加圖片', addQR: '添加二維碼', background: '背景設置',
                borders: '畫布邊框', print: '打印海報', textControls: '文字控制',
                imageControls: '圖片控制', qrControls: '二維碼控制',
                font: '字體', fontSize: '字體大小', fontColor: '字體顏色',
                textBorder: '文字邊框', borderWidth: '邊框寬度',
                borderColor: '邊框顏色', borderRadius: '邊框圓角',
                enterUrl: '輸入二維碼網址', generate: '生成二維碼',
                uploadImage: '點擊上傳或拖拽圖片到此處',
                supportedFormats: '支持格式：JPG, PNG, GIF', textColor: '文字顏色',
                textBackground: '文字背景', textShadow: '文字陰影',
                imageBorder: '圖片邊框',
                // File Management translations
                savePoster: '保存海報', posterName: '海報名稱', enterPosterName: '輸入海報名稱',
                cancel: '取消', save: '保存', exportJSON: '導出 JSON', loadPoster: '載入海報',
                noPreview: '無預覽', paper: '紙張', elements: '元素', load: '載入',
                importFromFile: '從文件導入', import: '導入', manageSavedPosters: '管理已保存海報',
                export: '導出', delete: '刪除', total: '總計', posters: '個海報',
                clearAll: '清空全部', close: '關閉', noSavedPosters: '沒有已保存的海報',
                noSavedPostersMessage: '您還沒有保存任何海報。',
                createPosterMessage: '創建並保存您的第一個海報開始使用！', ok: '確定',
                // Canvas Controls translations
                canvasControls: '畫布控制', toggleGrid: '切換網格',
                zoomIn: '放大', zoomOut: '縮小'
            }
        };
    }

    updateLanguage() {
        const t = this.translations[this.currentLanguage];

        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.dataset.translate;
            if (t[key]) {
                if (element.tagName === 'INPUT' && element.type !== 'button') {
                    element.placeholder = t[key];
                } else {
                    element.textContent = t[key];
                }
            }
        });

        document.querySelectorAll('[data-en][data-zh]').forEach(element => {
            const text = this.currentLanguage === 'en' ? element.dataset.en : element.dataset.zh;
            if (text) element.textContent = text;
        });

        document.querySelectorAll('[data-en-title][data-zh-title]').forEach(element => {
            const title = this.currentLanguage === 'en' ? element.dataset.enTitle : element.dataset.zhTitle;
            if (title) element.title = title;
        });

        const langToggle = document.getElementById('languageToggle');
        if (langToggle) {
            langToggle.textContent = this.currentLanguage === 'en' ? '中文' : 'English';
        }

        if (this.layerManager) this.layerManager.updateLayerControls();
        this.updateQuickColorDisplay();
    }

    changePaperSize(size) {
        this.currentPaperSize = size;
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        canvas.classList.remove('kms-canvas-letter', 'kms-canvas-4x6', 'kms-canvas-a4', 'kms-canvas-a3');
        canvas.classList.add(`kms-canvas-${size}`);

        const sizeConfig = {
            'letter': { width: '816px', height: '1056px' },
            '4x6': { width: '384px', height: '576px' },
            'a4': { width: '595px', height: '842px' },
            'a3': { width: '842px', height: '1191px' }
        };

        if (sizeConfig[size]) {
            canvas.style.width = sizeConfig[size].width;
            canvas.style.height = sizeConfig[size].height;
        }

        document.querySelectorAll('.kms-paper-option').forEach(option => {
            option.classList.toggle('active', option.dataset.size === size);
        });

        canvas.offsetHeight;
        
        // 重新計算縮放
        setTimeout(() => {
            this.handleCanvasResize();
            this.initializeZoomSlider();
        }, 50);
        this.repositionElementsInBounds();
        setTimeout(() => this.updateCanvasLayout(), 100);
    }

    setupCanvas() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        canvas.classList.add('kms-canvas-letter');
        const firstOption = document.querySelector('.kms-paper-option[data-size="letter"]');
        if (firstOption) firstOption.classList.add('active');

        canvas.style.display = 'block';
        canvas.style.visibility = 'visible';
        canvas.style.opacity = '1';

        const bgColorInput = document.getElementById('bgColor');
        if (bgColorInput) {
            this.changeBackgroundColor(bgColorInput.value || '#ffffff');
        }

        // 初始化 Paper Corner Radius
        const paperRadiusInput = document.getElementById('paperRadius');
        if (paperRadiusInput) {
            this.changePaperRadius(paperRadiusInput.value + 'px');
        }

        // 添加視窗大小變化監聽器
        this.setupResponsiveCanvas();
        window.addEventListener('resize', () => this.handleCanvasResize());
    }

    setupResponsiveCanvas() {
        this.handleCanvasResize();
    }

    handleCanvasResize() {
        const canvas = document.getElementById('posterCanvas');
        const container = document.querySelector('.kms-canvas-container');
        if (!canvas || !container) return;

        // 重置transform以獲取原始尺寸
        canvas.style.transform = 'none';
        
        // 獲取容器的實際可用空間（減去padding）
        const containerRect = container.getBoundingClientRect();
        const availableWidth = containerRect.width - 20; // 減去左右padding
        const availableHeight = containerRect.height - 20; // 減去上下padding
        
        // 獲取畫布的當前尺寸（根據紙張類型）
        let canvasWidth = 816; // Letter size default
        let canvasHeight = 1056;
        
        if (canvas.classList.contains('kms-canvas-a4')) {
            canvasWidth = 595;
            canvasHeight = 842;
        } else if (canvas.classList.contains('kms-canvas-a3')) {
            canvasWidth = 842;
            canvasHeight = 1191;
        } else if (canvas.classList.contains('kms-canvas-4x6')) {
            canvasWidth = 384;
            canvasHeight = 576;
        }
        
        // 計算縮放比例，確保畫布完全顯示在容器內
        const scaleX = availableWidth / canvasWidth;
        const scaleY = availableHeight / canvasHeight;
        const scale = Math.min(scaleX, scaleY, 1); // 不放大，只縮小
        
        // 應用縮放
        if (scale < 1) {
            canvas.style.transform = `scale(${scale})`;
        } else {
            canvas.style.transform = 'scale(1)';
        }
        
        console.log(`Canvas resize: ${canvasWidth}x${canvasHeight}, Available: ${availableWidth}x${availableHeight}, Scale: ${scale}`);
    }

    loadFonts() {
        // 使用本地字體或系統字體，避免CORS錯誤
        const style = document.createElement('style');
        style.textContent = `
            @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap');
            
            /* 備用字體設定 */
            .roboto-font {
                font-family: 'Roboto', 'Arial', 'Microsoft YaHei', '微軟雅黑', sans-serif;
            }
            
            .opensans-font {
                font-family: 'Open Sans', 'Arial', 'Microsoft YaHei', '微軟雅黑', sans-serif;
            }
        `;
        document.head.appendChild(style);
        
        // 如果Google Fonts載入失敗，使用系統字體
        setTimeout(() => {
            const testElement = document.createElement('div');
            testElement.style.fontFamily = 'Roboto';
            testElement.style.position = 'absolute';
            testElement.style.visibility = 'hidden';
            testElement.textContent = 'Test';
            document.body.appendChild(testElement);
            
            const computedFont = window.getComputedStyle(testElement).fontFamily;
            if (!computedFont.includes('Roboto')) {
                console.warn('Google Fonts載入失敗，使用系統字體');
            }
            
            document.body.removeChild(testElement);
        }, 1000);
    }

    addTextElement() {
        if (this.textHandler) {
            const textElement = this.textHandler.createTextElement();
            if (textElement) this.selectElement(textElement);
            return;
        }

        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const textElement = document.createElement('div');
        textElement.className = 'kms-canvas-element kms-text-element';
        textElement.contentEditable = true;
        textElement.textContent = this.currentLanguage === 'en' ? 'Double click to edit' : '雙擊編輯文字';
        Object.assign(textElement.style, {
            left: '50px', top: '50px', fontSize: '24px',
            fontFamily: 'Roboto, sans-serif', color: '#333333'
        });
        textElement.dataset.elementId = `text_${++this.elementCounter}`;
        this.addElementToCanvas(textElement);
    }

    setupElementInteraction(element) {
        element.addEventListener('mousedown', (e) => this.startDrag(e, element));
        if (element.classList.contains('kms-text-element')) {
            element.addEventListener('dblclick', (e) => {
                e.stopPropagation();
                element.focus();
            });
        }
        element.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectElement(element);
        });
    }

    startDrag(e, element) {
        if (this.dragDropHandler) {
            return this.dragDropHandler.startDrag(e, element);
        }

        if (e.target.contentEditable === 'true' && e.detail === 2) return;

        e.preventDefault();
        this.isDragging = true;
        this.selectedElement = element;

        const rect = element.getBoundingClientRect();
        const canvasRect = document.getElementById('posterCanvas').getBoundingClientRect();

        this.dragOffset = {
            x: e.clientX - canvasRect.left - parseInt(element.style.left || 0),
            y: e.clientY - canvasRect.top - parseInt(element.style.top || 0)
        };

        document.addEventListener('mousemove', this.handleDrag.bind(this));
        document.addEventListener('mouseup', this.stopDrag.bind(this));
        element.style.cursor = 'grabbing';
    }

    handleDrag(e) {
        if (!this.isDragging || !this.selectedElement) return;

        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();

        let newX = e.clientX - canvasRect.left - this.dragOffset.x;
        let newY = e.clientY - canvasRect.top - this.dragOffset.y;

        const elementWidth = parseInt(this.selectedElement.style.width) || this.selectedElement.offsetWidth;
        const elementHeight = parseInt(this.selectedElement.style.height) || this.selectedElement.offsetHeight;

        newX = Math.max(0, Math.min(newX, canvas.offsetWidth - elementWidth));
        newY = Math.max(0, Math.min(newY, canvas.offsetHeight - elementHeight));

        this.selectedElement.style.left = newX + 'px';
        this.selectedElement.style.top = newY + 'px';
    }

    stopDrag() {
        this.isDragging = false;
        if (this.selectedElement) this.selectedElement.style.cursor = 'move';
        document.removeEventListener('mousemove', this.handleDrag);
        document.removeEventListener('mouseup', this.stopDrag);
    }

    selectElement(element) {
        document.querySelectorAll('.kms-canvas-element').forEach(el => {
            el.classList.remove('selected');
        });

        element.classList.add('selected');
        this.selectedElement = element;
        this.updateControlsForElement(element);

        if (this.layerManager) {
            this.layerManager.updateLayerControls();
            this.layerManager.showLayerControls();
        }
    }

    hideAllControls() {
        const sections = ['textControlsSection', 'imageControlsSection', 'qrControlsSection'];
        sections.forEach(id => {
            const section = document.getElementById(id);
            if (section) section.style.display = 'none';
        });
    }

    updateControlsForElement(element) {
        this.hideAllControls();

        const rightLayerControlsSection = document.getElementById('rightLayerControlsSection');
        if (rightLayerControlsSection) {
            rightLayerControlsSection.style.display = 'block';
        }

        const controlMappings = {
            'kms-text-element': { section: 'textControlsSection', handler: this.textHandler },
            'kms-image-element': { section: 'imageControlsSection', handler: this.imageHandler },
            'kms-qr-element': { section: 'qrControlsSection', handler: this.qrGenerator }
        };

        Object.entries(controlMappings).forEach(([className, config]) => {
            if (element.classList.contains(className)) {
                const section = document.getElementById(config.section);
                if (section) {
                    section.style.display = 'block';
                    section.classList.remove('kms-hidden-section');
                }
                if (config.handler && config.handler.updateControlsForTextElement) {
                    config.handler.updateControlsForTextElement(element);
                } else if (config.handler && config.handler.updateControlsForImageElement) {
                    config.handler.updateControlsForImageElement(element);
                } else if (config.handler && config.handler.updateControlsForQRElement) {
                    config.handler.updateControlsForQRElement(element);
                }
            }
        });
    }

    handleCanvasClick(e) {
        if (e.target.id === 'posterCanvas') {
            this.selectedElement = null;
            document.querySelectorAll('.kms-canvas-element').forEach(el => {
                el.classList.remove('selected');
            });

            if (this.layerManager) {
                this.layerManager.updateLayerControls();
                this.layerManager.updateLayerList();
            }
            this.hideAllControls();
        }
    }

    handleKeyDown(e) {
        if (e.key === 'Delete' && this.selectedElement) {
            this.deleteSelectedElement();
        }
    }

    deleteSelectedElement() {
        if (this.selectedElement) {
            const elementToRemove = this.selectedElement;
            elementToRemove.remove();
            this.elements = this.elements.filter(el => el !== elementToRemove);

            if (this.layerManager) {
                this.layerManager.onElementRemoved(elementToRemove);
            }
            this.selectedElement = null;
        }
    }

    repositionElementsInBounds() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        this.elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            let left = parseInt(element.style.left) || 0;
            let top = parseInt(element.style.top) || 0;

            if (left + rect.width > canvas.offsetWidth) {
                left = canvas.offsetWidth - rect.width;
            }
            if (top + rect.height > canvas.offsetHeight) {
                top = canvas.offsetHeight - rect.height;
            }

            element.style.left = Math.max(0, left) + 'px';
            element.style.top = Math.max(0, top) + 'px';
        });
    }

    changeBackgroundColor(color) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) canvas.style.backgroundColor = color;

        const bgColorPreview = document.querySelector('#bgColor + .kms-color-preview');
        if (bgColorPreview) {
            bgColorPreview.style.backgroundColor = color;
            bgColorPreview.setAttribute('data-color', color);
        }
    }

    triggerImageUpload() {
        if (this.imageHandler) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = (e) => {
                if (e.target.files[0]) {
                    this.imageHandler.handleImageUpload(e.target.files[0]);
                }
            };
            input.click();
        }
    }

    showQRGenerator() {
        if (this.qrGenerator) {
            const qrControlsSection = document.getElementById('qrControlsSection');
            if (qrControlsSection) {
                qrControlsSection.style.display = 'block';
                qrControlsSection.classList.remove('kms-hidden-section');
                const urlInput = document.getElementById('qrUrl');
                if (urlInput) urlInput.focus();
            }
        }
    }

    printPoster() {
        if (this.printHandler) {
            this.printHandler.printPoster();
        } else {
            window.print();
        }
    }

    changeCanvasBorder(borderType) {
        const canvas = document.getElementById('posterCanvas');
        const borderElement = document.getElementById('canvasBorder');
        if (!canvas || !borderElement) return;

        borderElement.className = 'kms-canvas-border';
        if (borderType !== 'none') {
            borderElement.classList.add(`kms-border-${borderType}`);
        }

        document.querySelectorAll('.kms-canvas-border-option').forEach(option => {
            option.classList.toggle('active', option.dataset.border === borderType);
        });
    }

    toggleGrid() {
        const grid = document.getElementById('canvasGrid');
        const gridToggleBtn = document.getElementById('toggleGridBtn');
        if (grid) {
            grid.classList.toggle('active');
        }
        if (gridToggleBtn) {
            gridToggleBtn.classList.toggle('active');
        }
    }

    setCanvasZoom(scale) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        // 限制縮放範圍在 0.5 到 2.0 之間
        const clampedScale = Math.max(0.5, Math.min(2, scale));
        
        canvas.style.transform = `scale(${clampedScale})`;
        canvas.style.transformOrigin = 'center center';
        
        // 更新滑動條和顯示值
        const zoomSlider = document.getElementById('zoomSlider');
        const zoomValue = document.getElementById('zoomValue');
        if (zoomSlider && zoomValue) {
            const zoomPercent = Math.round(clampedScale * 100);
            zoomSlider.value = zoomPercent;
            zoomValue.textContent = zoomPercent + '%';
        }
    }

    initializeZoomSlider() {
        const canvas = document.getElementById('posterCanvas');
        const zoomSlider = document.getElementById('zoomSlider');
        const zoomValue = document.getElementById('zoomValue');
        
        if (!canvas || !zoomSlider || !zoomValue) return;
        
        // 獲取當前畫布縮放比例
        const currentTransform = canvas.style.transform || 'scale(1)';
        const currentScale = parseFloat(currentTransform.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const zoomPercent = Math.round(currentScale * 100);
        
        // 設置滑動條和顯示值
        zoomSlider.value = zoomPercent;
        zoomValue.textContent = zoomPercent + '%';
    }

    // 保留舊的方法名稱以保持向後兼容性
    zoomCanvas(factor) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        const currentTransform = canvas.style.transform || 'scale(1)';
        const currentScale = parseFloat(currentTransform.match(/scale\(([^)]+)\)/)?.[1] || 1);
        const newScale = currentScale * factor;
        
        this.setCanvasZoom(newScale);
    }

    changePaperRadius(radius) {
        const canvas = document.getElementById('posterCanvas');
        if (canvas) {
            canvas.style.setProperty('border-radius', radius, 'important');
        }
    }

    initializeLayerManager() {
        if (typeof KMSLayerManager !== 'undefined') {
            this.layerManager = new KMSLayerManager(this);
            this.layerManager.showLayerControls();
        }
    }

    addElementToCanvas(element) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        this.setupElementInteraction(element);
        canvas.appendChild(element);
        this.elements.push(element);

        if (this.layerManager) {
            this.layerManager.onElementAdded(element);
        }
        this.selectElement(element);
    }

    getAllElements() {
        return this.elements;
    }

    reorganizeElementLayers() {
        if (this.layerManager) {
            this.layerManager.reorganizeZIndexes();
        }
    }

    initializeEnhancedControls() {
        this.initializeRangeDisplays();
        this.initializeColorValueDisplays();
    }

    initializeRangeDisplays() {
        document.querySelectorAll('.kms-enhanced-range').forEach(range => {
            const updateDisplay = () => {
                const display = document.getElementById(range.id + 'Value');
                if (display) {
                    const unit = range.id.includes('Opacity') ? '%' : 'px';
                    display.textContent = range.value + unit;
                }
            };
            range.addEventListener('input', updateDisplay);
            updateDisplay();
        });
    }

    initializeColorValueDisplays() {
        document.querySelectorAll('.kms-enhanced-color-picker').forEach(picker => {
            const updateDisplay = () => {
                const colorRow = picker.closest('.kms-enhanced-color-row');
                if (colorRow) {
                    const colorValue = colorRow.querySelector('.kms-color-value');
                    if (colorValue) {
                        colorValue.textContent = picker.value.toUpperCase();
                    }
                }
            };
            picker.addEventListener('change', updateDisplay);
            picker.addEventListener('input', updateDisplay);
            updateDisplay();
        });
    }

    updateCanvasLayout() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;

        canvas.style.transform = 'translateZ(0)';
        setTimeout(() => {
            canvas.style.transform = '';
            this.elements.forEach(element => element.offsetHeight);
            if (this.layerManager) this.layerManager.updateLayerList();
        }, 50);
    }
}

document.addEventListener('DOMContentLoaded', () => {
    window.kmsPosterMaker = new KMSPosterMaker();
});