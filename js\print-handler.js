/**
 * <PERSON><PERSON> Poster Maker - Print Handler
 * 打印處理模塊
 */

class KMSPrintHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.init();
    }
    
    init() {
        this.setupPrintControls();
        this.createPrintStyles();
    }
    
    setupPrintControls() {
        const printBtn = document.getElementById('printBtn');
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                this.printPoster();
            });
        }
        
        // Add keyboard shortcut Ctrl+P
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.printPoster();
            }
        });
    }
    
    createPrintStyles() {
        const printStyles = document.createElement('style');
        printStyles.id = 'kms-print-styles';
        printStyles.textContent = `
            @media print {
                @page {
                    margin: 0 !important;
                    padding: 0 !important;
                    size: auto !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                @page :first {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                @page :left {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                @page :right {
                    margin: 0 !important;
                    padding: 0 !important;
                }
                
                * {
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                html {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                }
                
                body {
                    margin: 0 !important;
                    padding: 0 !important;
                    background: transparent !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                body * {
                    visibility: hidden;
                }
                
                .kms-poster-canvas,
                .kms-poster-canvas * {
                    visibility: visible !important;
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                }
                
                .kms-poster-canvas {
                    position: absolute !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    box-shadow: none !important;
                    border-radius: 0 !important;
                    transform: none !important;
                    page-break-inside: avoid !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    outline: none !important;
                }
                
                .kms-canvas-element.selected::after,
                .kms-resize-handle,
                .kms-canvas-tools,
                .kms-alignment-guide,
                .kms-position-indicator {
                    display: none !important;
                }
                
                .kms-canvas-grid {
                    display: none !important;
                }
                
                .kms-text-element {
                    padding: inherit !important;
                    line-height: inherit !important;
                    word-wrap: break-word !important;
                    white-space: pre-wrap !important;
                    background-color: inherit !important;
                    color: inherit !important;
                    font-weight: inherit !important;
                    font-style: inherit !important;
                    text-decoration: inherit !important;
                    text-align: inherit !important;
                    border: inherit !important;
                    border-radius: inherit !important;
                    text-shadow: inherit !important;
                    box-sizing: border-box !important;
                }
                
                /* Letter size specific */
                .kms-canvas-letter {
                    width: 8.5in !important;
                    height: 11in !important;
                    max-width: none !important;
                    max-height: none !important;
                }
                
                /* 4x6 size specific */
                .kms-canvas-4x6 {
                    width: 4in !important;
                    height: 6in !important;
                    max-width: none !important;
                    max-height: none !important;
                }
                
                /* Ensure all elements are properly sized for print */
                .kms-canvas-element {
                    max-width: none !important;
                    max-height: none !important;
                }
                
                .kms-text-element {
                    max-width: none !important;
                    max-height: none !important;
                }
                
                .kms-image-element {
                    max-width: none !important;
                    max-height: none !important;
                }
                
                .kms-qr-element {
                    max-width: none !important;
                    max-height: none !important;
                }
            }
        `;
        document.head.appendChild(printStyles);
    }
    
    printPoster() {
        // Show borderless print instructions first
        this.showBorderlessPrintInstructions();
    }
    
    showBorderlessPrintInstructions() {
        const overlay = document.createElement('div');
        overlay.className = 'kms-print-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const dialog = document.createElement('div');
        dialog.className = 'kms-borderless-dialog';
        dialog.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-height: 80vh;
            overflow-y: auto;
        `;
        
        const title = document.createElement('h2');
        title.textContent = this.posterMaker.currentLanguage === 'en' ? 'Borderless Printing Setup' : '無邊框打印設置';
        title.style.cssText = `
            margin: 0 0 1.5rem 0;
            color: #333;
            font-size: 1.5rem;
            text-align: center;
        `;
        
        const instructions = document.createElement('div');
        instructions.style.cssText = `
            margin-bottom: 2rem;
            line-height: 1.6;
            color: #555;
        `;
        
        const instructionText = this.posterMaker.currentLanguage === 'en' ? 
            `<p><strong>To achieve borderless printing, please follow these steps:</strong></p>
            <ol>
                <li>Click "Continue to Print" below</li>
                <li>In the print dialog, click "More settings"</li>
                <li>Set <strong>Margins</strong> to "None" or "Minimum"</li>
                <li>Enable <strong>"Background graphics"</strong></li>
                <li>Set <strong>Scale</strong> to "100%" or "Custom" and adjust as needed</li>
                <li>For best results, use <strong>"Actual size"</strong> option</li>
            </ol>
            <p><em>Note: Different browsers may have slightly different options. Look for "Margins: None" or similar settings.</em></p>` :
            `<p><strong>要實現無邊框打印，請按照以下步驟操作：</strong></p>
            <ol>
                <li>點擊下方的「繼續打印」</li>
                <li>在打印對話框中，點擊「更多設置」</li>
                <li>將<strong>邊距</strong>設置為「無」或「最小」</li>
                <li>啟用<strong>「背景圖形」</strong></li>
                <li>將<strong>縮放</strong>設置為「100%」或「自定義」並根據需要調整</li>
                <li>為獲得最佳效果，請使用<strong>「實際大小」</strong>選項</li>
            </ol>
            <p><em>注意：不同瀏覽器的選項可能略有不同。請尋找「邊距：無」或類似設置。</em></p>`;
        
        instructions.innerHTML = instructionText;
        
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            justify-content: center;
        `;
        
        const continueBtn = document.createElement('button');
        continueBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Continue to Print' : '繼續打印';
        continueBtn.style.cssText = `
            background: var(--color-1);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
        `;
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Cancel' : '取消';
        cancelBtn.style.cssText = `
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
        `;
        
        continueBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
            this.showPrintDialog();
        });
        
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(overlay);
        });
        
        buttonsContainer.appendChild(continueBtn);
        buttonsContainer.appendChild(cancelBtn);
        
        dialog.appendChild(title);
        dialog.appendChild(instructions);
        dialog.appendChild(buttonsContainer);
        overlay.appendChild(dialog);
        
        document.body.appendChild(overlay);
    }
    
    showPrintDialog() {
        // Create print dialog overlay
        const overlay = document.createElement('div');
        overlay.className = 'kms-print-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        const dialog = document.createElement('div');
        dialog.className = 'kms-print-dialog';
        dialog.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 2rem;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        `;
        
        const title = document.createElement('h2');
        title.textContent = this.posterMaker.currentLanguage === 'en' ? 'Print Settings' : '打印設置';
        title.style.cssText = `
            margin: 0 0 1.5rem 0;
            color: #333;
            font-size: 1.5rem;
        `;
        
        // Print options
        const optionsContainer = document.createElement('div');
        optionsContainer.style.marginBottom = '2rem';
        
        // Quality option
        const qualityGroup = this.createOptionGroup(
            this.posterMaker.currentLanguage === 'en' ? 'Print Quality' : '打印質量',
            [
                { value: 'draft', label: this.posterMaker.currentLanguage === 'en' ? 'Draft (Fast)' : '草稿 (快速)' },
                { value: 'normal', label: this.posterMaker.currentLanguage === 'en' ? 'Normal' : '普通', selected: true },
                { value: 'high', label: this.posterMaker.currentLanguage === 'en' ? 'High Quality' : '高質量' }
            ],
            'radio',
            'printQuality'
        );
        
        // Orientation option
        const orientationGroup = this.createOptionGroup(
            this.posterMaker.currentLanguage === 'en' ? 'Orientation' : '方向',
            [
                { value: 'portrait', label: this.posterMaker.currentLanguage === 'en' ? 'Portrait' : '縱向', selected: this.posterMaker.currentPaperSize === 'letter' },
                { value: 'landscape', label: this.posterMaker.currentLanguage === 'en' ? 'Landscape' : '橫向', selected: this.posterMaker.currentPaperSize === '4x6' }
            ],
            'radio',
            'printOrientation'
        );
        
        // Scale option
        const scaleGroup = this.createOptionGroup(
            this.posterMaker.currentLanguage === 'en' ? 'Scale' : '縮放',
            [
                { value: 'fit', label: this.posterMaker.currentLanguage === 'en' ? 'Fit to Page' : '適合頁面', selected: true },
                { value: 'actual', label: this.posterMaker.currentLanguage === 'en' ? 'Actual Size' : '實際大小' },
                { value: 'custom', label: this.posterMaker.currentLanguage === 'en' ? 'Custom' : '自定義' }
            ],
            'radio',
            'printScale'
        );
        
        optionsContainer.appendChild(qualityGroup);
        optionsContainer.appendChild(orientationGroup);
        optionsContainer.appendChild(scaleGroup);
        
        // Buttons
        const buttonsContainer = document.createElement('div');
        buttonsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        `;
        
        const cancelBtn = document.createElement('button');
        cancelBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Cancel' : '取消';
        cancelBtn.className = 'kms-btn kms-btn-secondary';
        cancelBtn.addEventListener('click', () => {
            overlay.remove();
        });
        
        const previewBtn = document.createElement('button');
        previewBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Preview' : '預覽';
        previewBtn.className = 'kms-btn';
        previewBtn.addEventListener('click', () => {
            this.showPrintPreview();
            overlay.remove();
        });
        
        const printBtn = document.createElement('button');
        printBtn.textContent = this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印';
        printBtn.className = 'kms-btn kms-btn-success';
        printBtn.addEventListener('click', () => {
            this.executePrint();
            overlay.remove();
        });
        
        buttonsContainer.appendChild(cancelBtn);
        buttonsContainer.appendChild(previewBtn);
        buttonsContainer.appendChild(printBtn);
        
        dialog.appendChild(title);
        dialog.appendChild(optionsContainer);
        dialog.appendChild(buttonsContainer);
        overlay.appendChild(dialog);
        document.body.appendChild(overlay);
        
        // Close on overlay click
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                overlay.remove();
            }
        });
    }
    
    createOptionGroup(title, options, type, name) {
        const group = document.createElement('div');
        group.style.marginBottom = '1.5rem';
        
        const label = document.createElement('label');
        label.textContent = title;
        label.style.cssText = `
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        `;
        
        const optionsContainer = document.createElement('div');
        optionsContainer.style.cssText = `
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        `;
        
        options.forEach(option => {
            const optionContainer = document.createElement('div');
            optionContainer.style.cssText = `
                display: flex;
                align-items: center;
                gap: 0.5rem;
            `;
            
            const input = document.createElement('input');
            input.type = type;
            input.name = name;
            input.value = option.value;
            input.checked = option.selected || false;
            input.id = `${name}_${option.value}`;
            
            const optionLabel = document.createElement('label');
            optionLabel.textContent = option.label;
            optionLabel.htmlFor = input.id;
            optionLabel.style.cursor = 'pointer';
            
            optionContainer.appendChild(input);
            optionContainer.appendChild(optionLabel);
            optionsContainer.appendChild(optionContainer);
        });
        
        group.appendChild(label);
        group.appendChild(optionsContainer);
        return group;
    }
    
    showPrintPreview() {
        // Create preview window with appropriate size
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        const windowWidth = Math.max(900, canvasRect.width + 100);
        const windowHeight = Math.max(700, canvasRect.height + 150);
        
        const previewWindow = window.open('', '_blank', `width=${windowWidth},height=${windowHeight}`);
        
        if (!previewWindow) {
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Please allow popups to show print preview'
                : '請允許彈出窗口以顯示打印預覽');
            return;
        }
        
        // Clone the canvas and all its content
        const canvasClone = canvas.cloneNode(true);
        
        // Reset any transform scaling on the cloned canvas
        canvasClone.style.transform = 'none';
        canvasClone.style.transformOrigin = 'initial';
        
        // Reset transform on all child elements and preserve original styles
        canvasClone.querySelectorAll('.kms-canvas-element').forEach(element => {
            element.style.transform = 'none';
            element.style.transformOrigin = 'initial';
            
            // For text elements, ensure all styles are preserved as inline styles
            if (element.classList.contains('kms-text-element')) {
                const originalElement = canvas.querySelector(`[data-element-id="${element.dataset.elementId}"]`);
                if (originalElement) {
                    // Copy all computed styles to inline styles
                    const computedStyle = window.getComputedStyle(originalElement);
                    element.style.fontSize = computedStyle.fontSize;
                    element.style.fontFamily = computedStyle.fontFamily;
                    element.style.color = computedStyle.color;
                    element.style.backgroundColor = computedStyle.backgroundColor;
                    element.style.textAlign = computedStyle.textAlign;
                    element.style.fontWeight = computedStyle.fontWeight;
                    element.style.fontStyle = computedStyle.fontStyle;
                    element.style.textDecoration = computedStyle.textDecoration;
                    element.style.borderRadius = computedStyle.borderRadius;
                    element.style.border = computedStyle.border;
                    element.style.padding = computedStyle.padding;
                    element.style.lineHeight = computedStyle.lineHeight;
                    element.style.textShadow = computedStyle.textShadow;
                    element.style.opacity = computedStyle.opacity;
                    element.style.boxShadow = computedStyle.boxShadow;
                }
            }
        });
        
        // Remove any selection indicators and resize handles
        canvasClone.querySelectorAll('.kms-canvas-element').forEach(element => {
            element.classList.remove('selected');
        });
        canvasClone.querySelectorAll('.kms-resize-handle').forEach(handle => {
            handle.remove();
        });
        
        const canvasHTML = canvasClone.outerHTML;
        
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${this.posterMaker.currentLanguage === 'en' ? 'Print Preview' : '打印預覽'}</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: Arial, sans-serif;
                        background: #f5f5f5;
                        overflow: auto;
                    }
                    .preview-container {
                        display: flex;
                        justify-content: center;
                        align-items: flex-start;
                        min-height: calc(100vh - 40px);
                        padding: 60px 20px 20px 20px;
                    }
                    .kms-poster-canvas {
                        background: white;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        position: relative;
                        overflow: hidden;
                    }
                    .kms-canvas-letter {
                        width: 816px !important;
                        height: 1056px !important;
                        max-width: none !important;
                        max-height: none !important;
                    }
                    .kms-canvas-4x6 {
                        width: 384px !important;
                        height: 576px !important;
                        max-width: none !important;
                        max-height: none !important;
                    }
                    /* Ensure all canvas content is visible */
                    .kms-poster-canvas * {
                        max-width: none !important;
                        max-height: none !important;
                    }
                    .kms-canvas-element {
                        position: absolute;
                    }
                    .kms-text-element {
                        word-wrap: break-word;
                        white-space: pre-wrap;
                        line-height: 1.2;
                        padding: 4px;
                        box-sizing: border-box !important;
                        transform: none !important;
                        zoom: 1 !important;
                        scale: 1 !important;
                    }
                    .kms-image-element img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                    .kms-qr-element img {
                        display: block;
                        width: 100%;
                        height: auto;
                    }
                    .kms-resize-handle,
                    .kms-canvas-element.selected::after {
                        display: none !important;
                    }
                    .print-controls {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        display: flex;
                        gap: 10px;
                    }
                    .btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 14px;
                    }
                    .btn-primary {
                        background: #667eea;
                        color: white;
                    }
                    .btn-secondary {
                        background: #6c757d;
                        color: white;
                    }
                    @media print {
                        @page {
                            margin: 0 !important;
                            padding: 0 !important;
                            size: auto !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                        
                        @page :first {
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        
                        @page :left {
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        
                        @page :right {
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        * {
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                        html {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: transparent !important;
                        }
                        .print-controls {
                            display: none !important;
                        }
                        body {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: transparent !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                        .preview-container {
                            margin: 0 !important;
                            padding: 0 !important;
                            min-height: auto !important;
                        }
                        .kms-poster-canvas {
                            box-shadow: none !important;
                            border-radius: 0 !important;
                            margin: 0 !important;
                            padding: 0 !important;
                            border: none !important;
                            outline: none !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="print-controls">
                    <button class="btn btn-primary" onclick="window.print()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印'}
                    </button>
                    <button class="btn btn-secondary" onclick="window.close()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Close' : '關閉'}
                    </button>
                </div>
                <div class="preview-container">
                    ${canvasHTML}
                </div>
            </body>
            </html>
        `);
        
        previewWindow.document.close();
    }
    
    executePrint() {
        // Prepare for printing
        this.preparePrintLayout();
        
        // Add print-specific styles temporarily
        const printStyleOverride = document.createElement('style');
        printStyleOverride.id = 'kms-print-override';
        printStyleOverride.textContent = `
            @media print {
                .kms-canvas-element.selected {
                    outline: none !important;
                }
                .kms-canvas-element.selected::after {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(printStyleOverride);
        
        // Clear selection before printing
        const selectedElement = this.posterMaker.selectedElement;
        if (selectedElement) {
            selectedElement.classList.remove('selected');
        }
        
        // Execute print
        setTimeout(() => {
            window.print();
            
            // Restore selection after print dialog
            setTimeout(() => {
                if (selectedElement) {
                    selectedElement.classList.add('selected');
                }
                printStyleOverride.remove();
            }, 1000);
        }, 100);
    }
    
    preparePrintLayout() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Store original styles
        const originalStyles = {
            position: canvas.style.position,
            transform: canvas.style.transform,
            boxShadow: canvas.style.boxShadow
        };
        
        // Apply print-ready styles
        canvas.style.position = 'relative';
        canvas.style.transform = 'none';
        canvas.style.boxShadow = 'none';
        
        // Restore after print (when print dialog closes)
        const mediaQueryList = window.matchMedia('print');
        const handlePrintEnd = () => {
            canvas.style.position = originalStyles.position;
            canvas.style.transform = originalStyles.transform;
            canvas.style.boxShadow = originalStyles.boxShadow;
            mediaQueryList.removeListener(handlePrintEnd);
        };
        
        mediaQueryList.addListener(handlePrintEnd);
    }
    
    // Export poster as image
    exportAsImage(format = 'png', quality = 0.9) {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Create a new canvas for export
        const exportCanvas = document.createElement('canvas');
        const ctx = exportCanvas.getContext('2d');
        
        // Set canvas size based on paper size
        const scale = 2; // Higher resolution
        if (this.posterMaker.currentPaperSize === 'letter') {
            exportCanvas.width = 816 * scale;
            exportCanvas.height = 1056 * scale;
        } else {
            exportCanvas.width = 384 * scale;
            exportCanvas.height = 576 * scale;
        }
        
        ctx.scale(scale, scale);
        
        // Fill background
        const bgColor = canvas.style.backgroundColor || '#ffffff';
        ctx.fillStyle = bgColor;
        ctx.fillRect(0, 0, exportCanvas.width / scale, exportCanvas.height / scale);
        
        // This would require html2canvas library for full implementation
        // For now, we'll use a simpler approach
        this.downloadCanvasAsImage(exportCanvas, format, quality);
    }
    
    downloadCanvasAsImage(canvas, format, quality) {
        const link = document.createElement('a');
        link.download = `poster_${Date.now()}.${format}`;
        link.href = canvas.toDataURL(`image/${format}`, quality);
        link.click();
    }
    
    // Save poster data
    savePosterData(posterName = null) {
        const canvas = document.getElementById('posterCanvas');
        const posterData = {
            id: Date.now().toString(),
            name: posterName || `Poster_${new Date().toLocaleDateString().replace(/\//g, '-')}`,
            paperSize: this.posterMaker.currentPaperSize,
            backgroundColor: canvas.style.backgroundColor || '#ffffff',
            elements: this.posterMaker.elements
                .sort((a, b) => (parseInt(b.style.zIndex) || 1) - (parseInt(a.style.zIndex) || 1))
                .map(element => {
                const elementData = {
                    id: element.dataset.elementId || this.generateElementId(),
                    type: element.classList.contains('kms-text-element') ? 'text' : 
                          element.classList.contains('kms-image-element') ? 'image' : 'qr',
                    position: {
                        x: parseInt(element.style.left) || 0,
                        y: parseInt(element.style.top) || 0
                    },
                    size: {
                        width: parseInt(element.style.width) || element.offsetWidth,
                        height: parseInt(element.style.height) || element.offsetHeight
                    },
                    zIndex: parseInt(element.style.zIndex) || 1,
                    styles: {
                        fontSize: element.style.fontSize,
                        fontFamily: element.style.fontFamily,
                        color: element.style.color,
                        backgroundColor: element.style.backgroundColor,
                        textAlign: element.style.textAlign,
                        fontWeight: element.style.fontWeight,
                        fontStyle: element.style.fontStyle,
                        textDecoration: element.style.textDecoration,
                        borderRadius: element.style.borderRadius,
                        border: element.style.border,
                        padding: element.style.padding,
                        lineHeight: element.style.lineHeight,
                        textShadow: element.style.textShadow,
                        transform: element.style.transform,
                        opacity: element.style.opacity,
                        boxShadow: element.style.boxShadow
                    }
                };
                
                // Add type-specific data
                if (elementData.type === 'text') {
                    elementData.content = element.textContent || element.innerText;
                } else if (elementData.type === 'image') {
                    const img = element.querySelector('img');
                    elementData.content = img ? img.src : '';
                    elementData.alt = img ? img.alt : '';
                } else if (elementData.type === 'qr') {
                    elementData.content = element.dataset.qrUrl || '';
                    elementData.qrSettings = {
                        size: element.dataset.qrSize || '90',
                        foreground: element.dataset.qrForeground || '#00ffff',
                        background: element.dataset.qrBackground || '#000000',
                        radius: element.dataset.qrRadius || '16',
                        border: element.dataset.qrBorder || '1'
                    };
                }
                
                return elementData;
            }),
            timestamp: new Date().toISOString(),
            version: '1.0'
        };
        
        // Save to localStorage
        this.saveToLocalStorage(posterData);
        
        // Show success message
        this.showSaveSuccessMessage(posterData.name);
        
        return posterData;
    }
    
    // Load poster data
    loadPosterData(posterData) {
        try {
            // Parse if it's a string
            if (typeof posterData === 'string') {
                posterData = JSON.parse(posterData);
            }
            
            // Clear current poster
            this.posterMaker.elements.forEach(element => element.remove());
            this.posterMaker.elements = [];
            
            // Clear selection
            this.posterMaker.selectedElement = null;
            
            // Hide all control sections
            const textControlsSection = document.getElementById('textControlsSection');
            const imageControlsSection = document.getElementById('imageControlsSection');
            const qrControlsSection = document.getElementById('qrControlsSection');
            const layerControlsSection = document.getElementById('layerControlsSection');

            if (textControlsSection) textControlsSection.style.display = 'none';
            if (imageControlsSection) imageControlsSection.style.display = 'none';
            if (qrControlsSection) qrControlsSection.style.display = 'none';
            if (layerControlsSection) layerControlsSection.style.display = 'none';
            
            // Set paper size
            this.posterMaker.changePaperSize(posterData.paperSize);
            
            // Set background color
            const canvas = document.getElementById('posterCanvas');
            canvas.style.backgroundColor = posterData.backgroundColor || '#ffffff';
            
            // Recreate elements in correct z-index order (lowest to highest)
            const sortedElements = posterData.elements.slice().sort((a, b) => (a.zIndex || 1) - (b.zIndex || 1));
            sortedElements.forEach(elementData => {
                this.recreateElement(elementData);
            });
            
            // Update layer manager if exists
            if (this.posterMaker.layerManager) {
                this.posterMaker.layerManager.updateLayerControls();
                this.posterMaker.layerManager.updateLayerList();
            }
            
            // Show success message
            this.showLoadSuccessMessage(posterData.name);
            
        } catch (error) {
            console.error('Error loading poster data:', error);
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Error loading poster data. Please check the file format.'
                : '加載海報數據時出錯，請檢查文件格式。');
        }
    }

    // Helper methods for save/load functionality
    generateElementId() {
        return 'element_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    saveToLocalStorage(posterData) {
        try {
            let savedPosters = JSON.parse(localStorage.getItem('kms_saved_posters') || '[]');
            
            // Remove existing poster with same ID if exists
            savedPosters = savedPosters.filter(p => p.id !== posterData.id);
            
            // Add new poster data
            savedPosters.unshift(posterData);
            
            // Keep only last 50 posters
            if (savedPosters.length > 50) {
                savedPosters = savedPosters.slice(0, 50);
            }
            
            localStorage.setItem('kms_saved_posters', JSON.stringify(savedPosters));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }
    
    getSavedPosters() {
        try {
            return JSON.parse(localStorage.getItem('kms_saved_posters') || '[]');
        } catch (error) {
            console.error('Error loading from localStorage:', error);
            return [];
        }
    }
    
    deleteSavedPoster(posterId) {
        try {
            let savedPosters = this.getSavedPosters();
            savedPosters = savedPosters.filter(p => p.id !== posterId);
            localStorage.setItem('kms_saved_posters', JSON.stringify(savedPosters));
            return true;
        } catch (error) {
            console.error('Error deleting poster:', error);
            return false;
        }
    }
    
    recreateElement(elementData) {
        let element;
        
        if (elementData.type === 'text') {
            // Create text element without adding to canvas (to avoid duplication)
            element = document.createElement('div');
            element.className = 'kms-canvas-element kms-text-element';
            element.contentEditable = true;
            element.textContent = elementData.content;
            
            // Add to canvas first
            const canvas = document.getElementById('posterCanvas');
            canvas.appendChild(element);
            
            // Setup interaction
            this.posterMaker.setupElementInteraction(element);
            
            // Add to elements array
            this.posterMaker.elements.push(element);
        } else if (elementData.type === 'image') {
            // Create image element without adding to canvas (to avoid duplication)
            element = document.createElement('div');
            element.className = 'kms-canvas-element kms-image-element';
            
            const img = document.createElement('img');
            img.src = elementData.content;
            img.alt = elementData.alt || '';
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'contain';
            img.style.borderRadius = '0px';
            img.draggable = false;
            element.appendChild(img);
            
            // Add to canvas first
            const canvas = document.getElementById('posterCanvas');
            canvas.appendChild(element);
            
            // Setup interaction
            this.posterMaker.setupElementInteraction(element);
            
            // Add to elements array
            this.posterMaker.elements.push(element);
        } else if (elementData.type === 'qr') {
            // Create QR element without adding to canvas (to avoid duplication)
            element = document.createElement('div');
            element.className = 'kms-canvas-element kms-qr-element';
            
            // Set QR data attributes
            element.dataset.qrUrl = elementData.content;
            element.dataset.qrSize = elementData.qrSettings?.size || '90';
            element.dataset.qrForeground = elementData.qrSettings?.foreground || '#00ffff';
            element.dataset.qrBackground = elementData.qrSettings?.background || '#000000';
            element.dataset.qrRadius = elementData.qrSettings?.radius || '16';
            element.dataset.qrBorder = elementData.qrSettings?.border || '1';
            
            // Generate QR Code
            const qrCanvas = this.posterMaker.qrGenerator.generateQRCode(
                elementData.content,
                parseInt(elementData.qrSettings?.size || '90'),
                elementData.qrSettings?.foreground || '#00ffff',
                elementData.qrSettings?.background || '#000000',
                parseInt(elementData.qrSettings?.border || '1')
            );
            
            if (qrCanvas) {
                const img = document.createElement('img');
                img.src = qrCanvas.toDataURL('image/png');
                img.alt = 'QR Code';
                img.style.width = '100%';
                img.style.height = '100%';
                img.style.display = 'block';
                img.style.pointerEvents = 'none';
                img.draggable = false;
                
                // Apply border radius if specified
                const radius = parseInt(elementData.qrSettings?.radius || '0');
                if (radius > 0) {
                    img.style.borderRadius = radius + 'px';
                    element.style.borderRadius = radius + 'px';
                }
                
                element.appendChild(img);
            }
            
            // Add to canvas first
            const canvas = document.getElementById('posterCanvas');
            canvas.appendChild(element);
            
            // Setup interaction
            this.posterMaker.setupElementInteraction(element);
            
            // Add to elements array
            this.posterMaker.elements.push(element);
        }
        
        if (element) {
            // Set element ID
            element.dataset.elementId = elementData.id;
            
            // Function to apply saved properties
            const applyElementProperties = () => {
                // Set position and size
                element.style.left = elementData.position.x + 'px';
                element.style.top = elementData.position.y + 'px';
                element.style.width = elementData.size.width + 'px';
                element.style.height = elementData.size.height + 'px';
                element.style.zIndex = elementData.zIndex || 1;
                
                // Apply styles
                Object.keys(elementData.styles).forEach(styleKey => {
                    if (elementData.styles[styleKey]) {
                        element.style[styleKey] = elementData.styles[styleKey];
                    }
                });
                
                // Don't call onElementAdded during loading as it would reassign z-index
                // Just update the layer controls and list
                if (this.posterMaker.layerManager) {
                    setTimeout(() => {
                        this.posterMaker.layerManager.updateLayerControls();
                        this.posterMaker.layerManager.updateLayerList();
                    }, 10);
                }
            };
            
            // For images, wait for load before applying properties
            if (elementData.type === 'image') {
                const img = element.querySelector('img');
                if (img && !img.complete) {
                    img.onload = applyElementProperties;
                } else {
                    applyElementProperties();
                }
            } else {
                // For text and QR elements, apply immediately
                applyElementProperties();
            }
        }
    }
    
    showSaveSuccessMessage(posterName) {
        const message = this.posterMaker.currentLanguage === 'en' 
            ? `Poster "${posterName}" saved successfully!`
            : `海報 "${posterName}" 儲存成功！`;
        this.showToast(message, 'success');
    }
    
    showLoadSuccessMessage(posterName) {
        const message = this.posterMaker.currentLanguage === 'en' 
            ? `Poster "${posterName}" loaded successfully!`
            : `海報 "${posterName}" 載入成功！`;
        this.showToast(message, 'success');
    }
    
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `kms-toast kms-toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? 'var(--color-3)' : type === 'error' ? 'var(--color-4)' : 'var(--color-1)'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            word-wrap: break-word;
            animation: slideInRight 0.3s ease-out;
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    
    // Export poster as JSON file
    exportPosterAsJSON() {
        const posterData = this.savePosterData();
        const dataStr = JSON.stringify(posterData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${posterData.name}.json`;
        link.click();
    }
    
    // Import poster from JSON file
    importPosterFromJSON(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const posterData = JSON.parse(e.target.result);
                this.loadPosterData(posterData);
            } catch (error) {
                console.error('Error importing poster:', error);
                alert(this.posterMaker.currentLanguage === 'en' 
                    ? 'Error importing poster file. Please check the file format.'
                    : '匯入海報文件時出錯，請檢查文件格式。');
            }
        };
        reader.readAsText(file);
    }
    
    showPrintPreview() {
        // Create preview window with appropriate size
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        const windowWidth = Math.max(900, canvasRect.width + 100);
        const windowHeight = Math.max(700, canvasRect.height + 150);
        
        const previewWindow = window.open('', '_blank', `width=${windowWidth},height=${windowHeight}`);
        
        if (!previewWindow) {
            alert(this.posterMaker.currentLanguage === 'en' 
                ? 'Please allow popups to show print preview'
                : '請允許彈出窗口以顯示打印預覽');
            return;
        }
        
        // Clone the canvas and all its content
        const canvasClone = canvas.cloneNode(true);
        
        // Reset any transform scaling on the cloned canvas
        canvasClone.style.transform = 'none';
        canvasClone.style.transformOrigin = 'initial';
        
        // Reset transform on all child elements and preserve original styles
        canvasClone.querySelectorAll('.kms-canvas-element').forEach(element => {
            element.style.transform = 'none';
            element.style.transformOrigin = 'initial';
            
            // For text elements, ensure all styles are preserved as inline styles
            if (element.classList.contains('kms-text-element')) {
                const originalElement = canvas.querySelector(`[data-element-id="${element.dataset.elementId}"]`);
                if (originalElement) {
                    // Copy all computed styles to inline styles
                    const computedStyle = window.getComputedStyle(originalElement);
                    element.style.fontSize = computedStyle.fontSize;
                    element.style.fontFamily = computedStyle.fontFamily;
                    element.style.color = computedStyle.color;
                    element.style.backgroundColor = computedStyle.backgroundColor;
                    element.style.textAlign = computedStyle.textAlign;
                    element.style.fontWeight = computedStyle.fontWeight;
                    element.style.fontStyle = computedStyle.fontStyle;
                    element.style.textDecoration = computedStyle.textDecoration;
                    element.style.borderRadius = computedStyle.borderRadius;
                    element.style.border = computedStyle.border;
                    element.style.padding = computedStyle.padding;
                    element.style.lineHeight = computedStyle.lineHeight;
                    element.style.textShadow = computedStyle.textShadow;
                    element.style.opacity = computedStyle.opacity;
                    element.style.boxShadow = computedStyle.boxShadow;
                }
            }
        });
        
        // Remove any selection indicators and resize handles
        canvasClone.querySelectorAll('.kms-canvas-element').forEach(element => {
            element.classList.remove('selected');
        });
        canvasClone.querySelectorAll('.kms-resize-handle').forEach(handle => {
            handle.remove();
        });
        
        const canvasHTML = canvasClone.outerHTML;
        
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${this.posterMaker.currentLanguage === 'en' ? 'Print Preview' : '打印預覽'}</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        font-family: Arial, sans-serif;
                        background: #f5f5f5;
                        overflow: auto;
                    }
                    .preview-container {
                        display: flex;
                        justify-content: center;
                        align-items: flex-start;
                        min-height: calc(100vh - 40px);
                        padding: 60px 20px 20px 20px;
                    }
                    .kms-poster-canvas {
                        background: white;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                        border-radius: 8px;
                        position: relative;
                        overflow: hidden;
                    }
                    .kms-canvas-letter {
                        width: 816px !important;
                        height: 1056px !important;
                        max-width: none !important;
                        max-height: none !important;
                    }
                    .kms-canvas-4x6 {
                        width: 384px !important;
                        height: 576px !important;
                        max-width: none !important;
                        max-height: none !important;
                    }
                    /* Ensure all canvas content is visible */
                    .kms-poster-canvas * {
                        max-width: none !important;
                        max-height: none !important;
                    }
                    .kms-canvas-element {
                        position: absolute;
                    }
                    .kms-text-element {
                        word-wrap: break-word;
                        white-space: pre-wrap;
                        box-sizing: border-box !important;
                        transform: none !important;
                        zoom: 1 !important;
                        scale: 1 !important;
                    }
                    .kms-image-element img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                    .kms-qr-element img {
                        display: block;
                        width: 100%;
                        height: auto;
                    }
                    .kms-resize-handle,
                    .kms-canvas-element.selected::after {
                        display: none !important;
                    }
                    .print-controls {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        display: flex;
                        gap: 10px;
                    }
                    .btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 14px;
                    }
                    .btn-primary {
                        background: #667eea;
                        color: white;
                    }
                    .btn-secondary {
                        background: #6c757d;
                        color: white;
                    }
                    @media print {
                        @page {
                            margin: 0 !important;
                            padding: 0 !important;
                            size: auto !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                        
                        @page :first {
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        
                        @page :left {
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        
                        @page :right {
                            margin: 0 !important;
                            padding: 0 !important;
                        }
                        * {
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                        html {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: transparent !important;
                        }
                        .print-controls {
                            display: none !important;
                        }
                        body {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: transparent !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                        .preview-container {
                            margin: 0 !important;
                            padding: 0 !important;
                            min-height: auto !important;
                        }
                        .kms-poster-canvas {
                            box-shadow: none !important;
                            border-radius: 0 !important;
                            margin: 0 !important;
                            padding: 0 !important;
                            border: none !important;
                            outline: none !important;
                            -webkit-print-color-adjust: exact !important;
                            color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="print-controls">
                    <button class="btn btn-primary" onclick="window.print()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Print' : '打印'}
                    </button>
                    <button class="btn btn-secondary" onclick="window.close()">
                        ${this.posterMaker.currentLanguage === 'en' ? 'Close' : '關閉'}
                    </button>
                </div>
                <div class="preview-container">
                    ${canvasHTML}
                </div>
            </body>
            </html>
        `);
        
        previewWindow.document.close();
    }
}