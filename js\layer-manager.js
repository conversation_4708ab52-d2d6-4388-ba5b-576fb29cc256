/**
 * KMS Poster Maker - Layer Management System
 * 分層管理系統
 */

class KMSLayerManager {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.baseZIndex = 100;
        this.maxZIndex = 1000;
        this.translations = {
            en: {
                bringToFront: 'Brought to front',
                bringForward: 'Moved forward',
                sendBackward: 'Moved backward',
                sendToBack: 'Sent to back',
                currentLayer: 'Current Layer',
                totalLayers: 'Total Layers',
                layerControls: 'Layer Controls',
                toFront: 'To Front',
                forward: 'Forward',
                backward: 'Backward',
                toBack: 'To Back',
                deleteLayer: 'Delete Layer',
                layerDeleted: 'Layer deleted',
                lockLayer: 'Lock Layer',
                unlockLayer: 'Unlock Layer',
                layerLocked: 'Layer locked',
                layerUnlocked: 'Layer unlocked',
                cannotDeleteLockedLayer: 'Cannot delete locked layer. Please unlock it first.',
                cannotMoveLockedLayer: 'Cannot move locked layer. Please unlock it first.'
            },
            zh: {
                bringToFront: '已置頂',
                bringForward: '已向前移動',
                sendBackward: '已向後移動',
                sendToBack: '已置底',
                currentLayer: '當前圖層',
                totalLayers: '總圖層數',
                layerControls: '圖層控制',
                toFront: '置頂',
                forward: '向前',
                backward: '向後',
                toBack: '置底',
                deleteLayer: '刪除圖層',
                layerDeleted: '圖層已刪除',
                lockLayer: '鎖定圖層',
                unlockLayer: '解鎖圖層',
                layerLocked: '圖層已鎖定',
                layerUnlocked: '圖層已解鎖',
                cannotDeleteLockedLayer: '無法刪除已鎖定的圖層，請先解鎖。',
                cannotMoveLockedLayer: '無法移動已鎖定的圖層，請先解鎖。'
            }
        };
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateLayerControls();
    }
    
    setupEventListeners() {
        // Right panel layer control buttons
        document.getElementById('rightBringToFrontBtn')?.addEventListener('click', () => {
            if (this.posterMaker.selectedElement) {
                this.bringToFront(this.posterMaker.selectedElement);
            }
        });

        document.getElementById('rightBringForwardBtn')?.addEventListener('click', () => {
            if (this.posterMaker.selectedElement) {
                this.bringForward(this.posterMaker.selectedElement);
            }
        });

        document.getElementById('rightSendBackwardBtn')?.addEventListener('click', () => {
            if (this.posterMaker.selectedElement) {
                this.sendBackward(this.posterMaker.selectedElement);
            }
        });

        document.getElementById('rightSendToBackBtn')?.addEventListener('click', () => {
            if (this.posterMaker.selectedElement) {
                this.sendToBack(this.posterMaker.selectedElement);
            }
        });
    }
    
    /**
     * 獲取元素的當前z-index
     */
    getElementZIndex(element) {
        const zIndex = parseInt(window.getComputedStyle(element).zIndex) || this.baseZIndex;
        return zIndex;
    }
    
    /**
     * 設置元素的z-index
     */
    setElementZIndex(element, zIndex) {
        element.style.zIndex = zIndex;
        element.dataset.layerIndex = zIndex;
        this.updateLayerControls();
        this.updateLayerList();
    }
    
    /**
     * 獲取所有元素按z-index排序
     */
    getAllElementsSorted() {
        return this.posterMaker.elements
            .slice()
            .sort((a, b) => this.getElementZIndex(a) - this.getElementZIndex(b));
    }
    
    /**
     * 獲取元素在分層中的位置（從1開始）
     */
    getElementLayerPosition(element) {
        const sortedElements = this.getAllElementsSorted();
        return sortedElements.indexOf(element) + 1;
    }
    
    /**
     * 置頂元素
     */
    bringToFront(element) {
        if (this.isElementLocked(element)) {
            alert(this.getTranslation('cannotMoveLockedLayer'));
            return;
        }
        const maxZ = Math.max(
            ...this.posterMaker.elements.map(el => this.getElementZIndex(el)),
            this.baseZIndex
        );
        this.setElementZIndex(element, Math.min(maxZ + 1, this.maxZIndex));
        this.showLayerFeedback(element, this.getTranslation('bringToFront'));
    }
    
    /**
     * 向前移動一層
     */
    bringForward(element) {
        if (this.isElementLocked(element)) {
            alert(this.getTranslation('cannotMoveLockedLayer'));
            return;
        }
        const currentZ = this.getElementZIndex(element);
        const sortedElements = this.getAllElementsSorted();
        const currentIndex = sortedElements.indexOf(element);

        if (currentIndex < sortedElements.length - 1) {
            const nextElement = sortedElements[currentIndex + 1];
            const nextZ = this.getElementZIndex(nextElement);

            // 交換z-index
            this.setElementZIndex(element, nextZ);
            this.setElementZIndex(nextElement, currentZ);
            this.showLayerFeedback(element, this.getTranslation('bringForward'));
        }
    }
    
    /**
     * 向後移動一層
     */
    sendBackward(element) {
        if (this.isElementLocked(element)) {
            alert(this.getTranslation('cannotMoveLockedLayer'));
            return;
        }
        const currentZ = this.getElementZIndex(element);
        const sortedElements = this.getAllElementsSorted();
        const currentIndex = sortedElements.indexOf(element);

        if (currentIndex > 0) {
            const prevElement = sortedElements[currentIndex - 1];
            const prevZ = this.getElementZIndex(prevElement);

            // 交換z-index
            this.setElementZIndex(element, prevZ);
            this.setElementZIndex(prevElement, currentZ);
            this.showLayerFeedback(element, this.getTranslation('sendBackward'));
        }
    }
    
    /**
     * 置底元素
     */
    sendToBack(element) {
        if (this.isElementLocked(element)) {
            alert(this.getTranslation('cannotMoveLockedLayer'));
            return;
        }
        const minZ = Math.min(
            ...this.posterMaker.elements.map(el => this.getElementZIndex(el)),
            this.baseZIndex
        );
        this.setElementZIndex(element, Math.max(minZ - 1, 1));
        this.showLayerFeedback(element, this.getTranslation('sendToBack'));
    }
    
    /**
     * 為新元素分配z-index
     */
    assignZIndexToNewElement(element) {
        const maxZ = this.posterMaker.elements.length > 0 
            ? Math.max(...this.posterMaker.elements.map(el => this.getElementZIndex(el)))
            : this.baseZIndex;
        
        this.setElementZIndex(element, maxZ + 1);
    }
    
    /**
     * 更新分層控制按鈕狀態
     */
    updateLayerControls() {
        const selectedElement = this.posterMaker.selectedElement;
        const bringToFrontBtn = document.getElementById('rightBringToFrontBtn');
        const bringForwardBtn = document.getElementById('rightBringForwardBtn');
        const sendBackwardBtn = document.getElementById('rightSendBackwardBtn');
        const sendToBackBtn = document.getElementById('rightSendToBackBtn');

        if (!selectedElement || this.posterMaker.elements.length <= 1) {
            // 禁用所有按鈕
            [bringToFrontBtn, bringForwardBtn, sendBackwardBtn, sendToBackBtn].forEach(btn => {
                if (btn) btn.disabled = true;
            });
            return;
        }

        const sortedElements = this.getAllElementsSorted();
        const currentIndex = sortedElements.indexOf(selectedElement);
        const isTopmost = currentIndex === sortedElements.length - 1;
        const isBottommost = currentIndex === 0;

        // 更新按鈕狀態
        if (bringToFrontBtn) bringToFrontBtn.disabled = isTopmost;
        if (bringForwardBtn) bringForwardBtn.disabled = isTopmost;
        if (sendBackwardBtn) sendBackwardBtn.disabled = isBottommost;
        if (sendToBackBtn) sendToBackBtn.disabled = isBottommost;

        // 更新分層信息顯示
        this.updateLayerInfo(selectedElement);
    }
    
    /**
     * 更新分層信息顯示
     */
    updateLayerInfo(element) {
        const currentLayerValue = document.getElementById('rightCurrentLayerValue');
        const totalLayersValue = document.getElementById('rightTotalLayersValue');

        if (element && currentLayerValue && totalLayersValue) {
            const layerPosition = this.getElementLayerPosition(element);
            const totalLayers = this.posterMaker.elements.length;

            currentLayerValue.textContent = layerPosition;
            totalLayersValue.textContent = totalLayers;
        }
    }
    
    /**
     * 更新分層列表
     */
    updateLayerList() {
        const container = document.getElementById('rightLayerListContainer');
        if (!container) return;

        container.innerHTML = '';

        const sortedElements = this.getAllElementsSorted(); // 從底層開始顯示
        const reversedElements = sortedElements.slice().reverse(); // 反轉順序，上層元素在上方

        reversedElements.forEach((element, index) => {
            const layerItem = this.createLayerItem(element, sortedElements.length - index);
            container.appendChild(layerItem);
        });
    }
    
    /**
     * 創建分層列表項目
     */
    createLayerItem(element, layerNumber) {
        const item = document.createElement('div');
        item.className = 'kms-layer-item';
        if (element === this.posterMaker.selectedElement) {
            item.classList.add('selected');
        }
        
        const elementType = this.getElementType(element);
        const elementName = this.getElementName(element, elementType);
        
        const isLocked = element.dataset.locked === 'true';

        item.innerHTML = `
            <div class="kms-layer-item-info">
                <div class="kms-layer-item-icon">
                    <i class="${this.getElementIcon(elementType)}"></i>
                </div>
                <div class="kms-layer-item-name">${elementName}</div>
            </div>
            <div class="kms-layer-item-actions">
                <div class="kms-layer-item-index">${layerNumber}</div>
                <button class="kms-layer-lock-btn ${isLocked ? 'locked' : ''}" title="${isLocked ? this.getTranslation('unlockLayer') : this.getTranslation('lockLayer')}">
                    <i class="fas ${isLocked ? 'fa-lock' : 'fa-unlock'}"></i>
                </button>
                <button class="kms-layer-delete-btn" title="${this.getTranslation('deleteLayer')}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // 點擊選擇元素
        const itemInfo = item.querySelector('.kms-layer-item-info');
        itemInfo.addEventListener('click', () => {
            this.posterMaker.selectElement(element);
            this.updateLayerList();
        });

        // 鎖定按鈕
        const lockBtn = item.querySelector('.kms-layer-lock-btn');
        lockBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleElementLock(element);
        });

        // 刪除按鈕事件
        const deleteBtn = item.querySelector('.kms-layer-delete-btn');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteElement(element);
        });

        return item;
    }
    
    /**
     * 獲取元素類型
     */
    getElementType(element) {
        if (element.classList.contains('kms-text-element')) return 'text';
        if (element.classList.contains('kms-image-element')) return 'image';
        if (element.classList.contains('kms-qr-element')) return 'qr';
        return 'unknown';
    }
    
    /**
     * 獲取元素名稱
     */
    getElementName(element, type) {
        switch (type) {
            case 'text':
                const textContent = element.textContent || element.innerText || '';
                return textContent.length > 20 
                    ? textContent.substring(0, 20) + '...' 
                    : textContent || 'Text Element';
            case 'image':
                return 'Image Element';
            case 'qr':
                return 'QR Code';
            default:
                return 'Element';
        }
    }
    
    /**
     * 獲取元素圖標
     */
    getElementIcon(type) {
        switch (type) {
            case 'text': return 'fas fa-font';
            case 'image': return 'fas fa-image';
            case 'qr': return 'fas fa-qrcode';
            default: return 'fas fa-square';
        }
    }
    
    /**
     * 獲取翻譯文字
     */
    getTranslation(key) {
        const lang = this.posterMaker.currentLanguage || 'zh';
        return this.translations[lang]?.[key] || this.translations.zh[key];
    }
    
    /**
     * 顯示分層操作反饋
     */
    showLayerFeedback(element, action) {
        // 創建反饋提示
        const feedback = document.createElement('div');
        feedback.className = 'kms-layer-feedback';
        feedback.textContent = action;
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--color-3);
            color: var(--text-color-1);
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-weight: 600;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(feedback);
        
        // 顯示動畫
        requestAnimationFrame(() => {
            feedback.style.opacity = '1';
        });
        
        // 自動移除
        setTimeout(() => {
            feedback.style.opacity = '0';
            setTimeout(() => {
                if (feedback.parentNode) {
                    feedback.parentNode.removeChild(feedback);
                }
            }, 300);
        }, 1500);
    }
    
    /**
     * 顯示分層控制面板
     */
    showLayerControls() {
        const section = document.getElementById('rightLayerControlsSection');
        if (section) {
            section.style.display = 'block';
            this.updateLayerControls();
            this.updateLayerList();
        }
    }

    /**
     * 隱藏分層控制面板
     */
    hideLayerControls() {
        const section = document.getElementById('rightLayerControlsSection');
        if (section) {
            section.style.display = 'none';
        }
    }
    
    /**
     * 重新整理所有元素的z-index
     */
    reorganizeZIndexes() {
        const sortedElements = this.getAllElementsSorted();
        sortedElements.forEach((element, index) => {
            element.style.zIndex = this.baseZIndex + index;
            element.dataset.layerIndex = this.baseZIndex + index;
        });
        this.updateLayerControls();
        this.updateLayerList();
    }
    
    /**
     * 切換元素鎖定狀態
     */
    toggleElementLock(element) {
        const isLocked = element.dataset.locked === 'true';
        element.dataset.locked = !isLocked;

        // 更新元素樣式以顯示鎖定狀態
        if (!isLocked) {
            element.style.pointerEvents = 'none';
            element.classList.add('kms-locked-element');
        } else {
            element.style.pointerEvents = 'auto';
            element.classList.remove('kms-locked-element');
        }

        // 如果當前選中的元素被鎖定，取消選中
        if (!isLocked && this.posterMaker.selectedElement === element) {
            this.posterMaker.selectElement(null);
        }

        this.updateLayerList();
        this.updateLayerControls();

        // 顯示鎖定狀態反饋
        const action = !isLocked ? this.getTranslation('layerLocked') : this.getTranslation('layerUnlocked');
        this.showLayerFeedback(element, action);
    }

    /**
     * 檢查元素是否被鎖定
     */
    isElementLocked(element) {
        return element.dataset.locked === 'true';
    }

    /**
     * 刪除元素
     */
    deleteElement(element) {
        if (!element || !element.parentNode) return;

        // 檢查元素是否被鎖定
        if (this.isElementLocked(element)) {
            alert(this.getTranslation('cannotDeleteLockedLayer'));
            return;
        }

        // 如果是選中的元素，清除選中狀態
        if (element === this.posterMaker.selectedElement) {
            this.posterMaker.selectedElement = null;
            this.posterMaker.hideAllControls();
        }

        // 從elements陣列中移除元素
        this.posterMaker.elements = this.posterMaker.elements.filter(el => el !== element);

        // 從DOM中移除元素
        element.parentNode.removeChild(element);

        // 更新圖層列表和控制項
        this.updateLayerList();
        this.updateLayerControls();
        this.reorganizeZIndexes();

        // 顯示刪除反饋
        this.showLayerFeedback(element, this.getTranslation('layerDeleted'));
    }
    
    /**
     * 移除元素時更新分層
     */
    onElementRemoved(element) {
        this.updateLayerControls();
        this.updateLayerList();
    }
    
    /**
     * 添加元素時更新分層
     */
    onElementAdded(element) {
        this.assignZIndexToNewElement(element);
        this.updateLayerControls();
        this.updateLayerList();
    }
}

// 導出類以供其他模塊使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KMSLayerManager;
}