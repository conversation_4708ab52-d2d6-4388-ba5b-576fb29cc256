/**
 * K<PERSON> Poster Maker - Drag and Drop Handler
 * 拖拽處理模塊
 */

class KMSDragDropHandler {
    constructor(posterMaker) {
        this.posterMaker = posterMaker;
        this.isDragging = false;
        this.dragElement = null;
        this.dragOffset = { x: 0, y: 0 };
        this.snapThreshold = 10; // pixels
        this.gridSize = 20; // pixels
        this.init();
    }
    
    init() {
        this.setupCanvasInteractions();
        this.setupKeyboardShortcuts();
    }
    
    setupCanvasInteractions() {
        const canvas = document.getElementById('posterCanvas');
        if (!canvas) return;
        
        // Prevent default drag behavior
        canvas.addEventListener('dragstart', (e) => e.preventDefault());
        canvas.addEventListener('selectstart', (e) => e.preventDefault());
        
        // Global mouse events for dragging
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // Touch events for mobile support
        document.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e));
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (!this.posterMaker.selectedElement) return;

            // Don't move locked elements
            if (this.posterMaker.selectedElement.dataset.locked === 'true') {
                return;
            }

            const step = e.shiftKey ? 10 : 1;
            let moved = false;

            switch (e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    this.moveElement(this.posterMaker.selectedElement, 0, -step);
                    moved = true;
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.moveElement(this.posterMaker.selectedElement, 0, step);
                    moved = true;
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.moveElement(this.posterMaker.selectedElement, -step, 0);
                    moved = true;
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.moveElement(this.posterMaker.selectedElement, step, 0);
                    moved = true;
                    break;
            }

            if (moved) {
                this.showPositionIndicator(this.posterMaker.selectedElement);
            }
        });
    }
    
    startDrag(e, element) {
        // Don't start drag if clicking on resize handles or during text editing
        if (e.target.classList.contains('kms-resize-handle') ||
            (element.contentEditable === 'true' && e.detail === 2)) {
            return false;
        }

        // Don't start drag if element is locked
        if (element.dataset.locked === 'true') {
            return false;
        }

        e.preventDefault();
        e.stopPropagation();

        this.isDragging = true;
        this.dragElement = element;
        
        const rect = element.getBoundingClientRect();
        const canvasRect = document.getElementById('posterCanvas').getBoundingClientRect();
        
        // Calculate offset from mouse to element's top-left corner
        this.dragOffset = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top
        };
        
        // Add dragging class for visual feedback
        element.classList.add('dragging');
        document.body.style.cursor = 'grabbing';
        
        // Show grid if enabled
        this.showGrid();
        
        return true;
    }
    
    handleMouseMove(e) {
        if (!this.isDragging || !this.dragElement) return;
        
        this.updateElementPosition(e.clientX, e.clientY);
    }
    
    handleTouchMove(e) {
        if (!this.isDragging || !this.dragElement) return;
        
        e.preventDefault();
        const touch = e.touches[0];
        this.updateElementPosition(touch.clientX, touch.clientY);
    }
    
    updateElementPosition(clientX, clientY) {
        const canvas = document.getElementById('posterCanvas');
        const canvasRect = canvas.getBoundingClientRect();
        
        // Calculate new position
        let newX = clientX - canvasRect.left - this.dragOffset.x;
        let newY = clientY - canvasRect.top - this.dragOffset.y;
        
        // Apply grid snapping if enabled
        if (this.isGridEnabled()) {
            newX = Math.round(newX / this.gridSize) * this.gridSize;
            newY = Math.round(newY / this.gridSize) * this.gridSize;
        }
        
        // Apply element snapping
        const snappedPosition = this.getSnappedPosition(newX, newY);
        newX = snappedPosition.x;
        newY = snappedPosition.y;
        
        // Constrain to canvas bounds
        const elementRect = this.dragElement.getBoundingClientRect();
        const maxX = canvas.offsetWidth - elementRect.width;
        const maxY = canvas.offsetHeight - elementRect.height;
        
        newX = Math.max(0, Math.min(newX, maxX));
        newY = Math.max(0, Math.min(newY, maxY));
        
        // Apply position
        this.dragElement.style.left = newX + 'px';
        this.dragElement.style.top = newY + 'px';
        
        // Show alignment guides
        this.showAlignmentGuides(newX, newY);
    }
    
    handleMouseUp(e) {
        this.stopDrag();
    }
    
    handleTouchEnd(e) {
        this.stopDrag();
    }
    
    stopDrag() {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        
        if (this.dragElement) {
            this.dragElement.classList.remove('dragging');
            this.dragElement = null;
        }
        
        document.body.style.cursor = '';
        this.hideGrid();
        this.hideAlignmentGuides();
        this.hidePositionIndicator();
    }
    
    moveElement(element, deltaX, deltaY) {
        const currentX = parseInt(element.style.left) || 0;
        const currentY = parseInt(element.style.top) || 0;
        
        let newX = currentX + deltaX;
        let newY = currentY + deltaY;
        
        // Constrain to canvas bounds
        const canvas = document.getElementById('posterCanvas');
        const elementRect = element.getBoundingClientRect();
        const maxX = canvas.offsetWidth - elementRect.width;
        const maxY = canvas.offsetHeight - elementRect.height;
        
        newX = Math.max(0, Math.min(newX, maxX));
        newY = Math.max(0, Math.min(newY, maxY));
        
        element.style.left = newX + 'px';
        element.style.top = newY + 'px';
    }
    
    getSnappedPosition(x, y) {
        const canvas = document.getElementById('posterCanvas');
        const elements = this.posterMaker.elements.filter(el => el !== this.dragElement);
        
        let snappedX = x;
        let snappedY = y;
        
        // Snap to other elements
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            const elementX = rect.left - canvasRect.left;
            const elementY = rect.top - canvasRect.top;
            const elementRight = elementX + rect.width;
            const elementBottom = elementY + rect.height;
            
            const dragRect = this.dragElement.getBoundingClientRect();
            const dragRight = x + dragRect.width;
            const dragBottom = y + dragRect.height;
            
            // Horizontal snapping
            if (Math.abs(x - elementX) < this.snapThreshold) {
                snappedX = elementX; // Left to left
            } else if (Math.abs(x - elementRight) < this.snapThreshold) {
                snappedX = elementRight; // Left to right
            } else if (Math.abs(dragRight - elementX) < this.snapThreshold) {
                snappedX = elementX - dragRect.width; // Right to left
            } else if (Math.abs(dragRight - elementRight) < this.snapThreshold) {
                snappedX = elementRight - dragRect.width; // Right to right
            }
            
            // Vertical snapping
            if (Math.abs(y - elementY) < this.snapThreshold) {
                snappedY = elementY; // Top to top
            } else if (Math.abs(y - elementBottom) < this.snapThreshold) {
                snappedY = elementBottom; // Top to bottom
            } else if (Math.abs(dragBottom - elementY) < this.snapThreshold) {
                snappedY = elementY - dragRect.height; // Bottom to top
            } else if (Math.abs(dragBottom - elementBottom) < this.snapThreshold) {
                snappedY = elementBottom - dragRect.height; // Bottom to bottom
            }
        });
        
        // Snap to canvas edges
        if (Math.abs(x) < this.snapThreshold) {
            snappedX = 0; // Left edge
        } else if (Math.abs(x + this.dragElement.offsetWidth - canvas.offsetWidth) < this.snapThreshold) {
            snappedX = canvas.offsetWidth - this.dragElement.offsetWidth; // Right edge
        }
        
        if (Math.abs(y) < this.snapThreshold) {
            snappedY = 0; // Top edge
        } else if (Math.abs(y + this.dragElement.offsetHeight - canvas.offsetHeight) < this.snapThreshold) {
            snappedY = canvas.offsetHeight - this.dragElement.offsetHeight; // Bottom edge
        }
        
        return { x: snappedX, y: snappedY };
    }
    
    showAlignmentGuides(x, y) {
        this.hideAlignmentGuides();
        
        const canvas = document.getElementById('posterCanvas');
        const elements = this.posterMaker.elements.filter(el => el !== this.dragElement);
        
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const canvasRect = canvas.getBoundingClientRect();
            
            const elementX = rect.left - canvasRect.left;
            const elementY = rect.top - canvasRect.top;
            const elementCenterX = elementX + rect.width / 2;
            const elementCenterY = elementY + rect.height / 2;
            
            const dragRect = this.dragElement.getBoundingClientRect();
            const dragCenterX = x + dragRect.width / 2;
            const dragCenterY = y + dragRect.height / 2;
            
            // Show vertical guide for center alignment
            if (Math.abs(dragCenterX - elementCenterX) < this.snapThreshold) {
                this.createAlignmentGuide('vertical', elementCenterX);
            }
            
            // Show horizontal guide for center alignment
            if (Math.abs(dragCenterY - elementCenterY) < this.snapThreshold) {
                this.createAlignmentGuide('horizontal', elementCenterY);
            }
        });
    }
    
    createAlignmentGuide(type, position) {
        const canvas = document.getElementById('posterCanvas');
        const guide = document.createElement('div');
        guide.className = `kms-alignment-guide kms-alignment-guide-${type}`;
        
        guide.style.position = 'absolute';
        guide.style.backgroundColor = '#667eea';
        guide.style.zIndex = '15';
        guide.style.pointerEvents = 'none';
        
        if (type === 'vertical') {
            guide.style.left = position + 'px';
            guide.style.top = '0';
            guide.style.width = '1px';
            guide.style.height = '100%';
        } else {
            guide.style.left = '0';
            guide.style.top = position + 'px';
            guide.style.width = '100%';
            guide.style.height = '1px';
        }
        
        canvas.appendChild(guide);
    }
    
    hideAlignmentGuides() {
        const guides = document.querySelectorAll('.kms-alignment-guide');
        guides.forEach(guide => guide.remove());
    }
    
    showGrid() {
        const grid = document.getElementById('canvasGrid');
        if (grid && this.isGridEnabled()) {
            grid.classList.add('active');
        }
    }
    
    hideGrid() {
        const grid = document.getElementById('canvasGrid');
        if (grid) {
            grid.classList.remove('active');
        }
    }
    
    isGridEnabled() {
        const gridToggleBtn = document.getElementById('toggleGridBtn');
        return gridToggleBtn && gridToggleBtn.classList.contains('active');
    }
    
    showPositionIndicator(element) {
        this.hidePositionIndicator();
        
        const x = parseInt(element.style.left) || 0;
        const y = parseInt(element.style.top) || 0;
        
        const indicator = document.createElement('div');
        indicator.className = 'kms-position-indicator';
        indicator.textContent = `${x}, ${y}`;
        indicator.style.position = 'fixed';
        indicator.style.background = 'rgba(0,0,0,0.8)';
        indicator.style.color = 'white';
        indicator.style.padding = '4px 8px';
        indicator.style.borderRadius = '4px';
        indicator.style.fontSize = '12px';
        indicator.style.zIndex = '1000';
        indicator.style.pointerEvents = 'none';
        
        const rect = element.getBoundingClientRect();
        indicator.style.left = (rect.left + rect.width / 2) + 'px';
        indicator.style.top = (rect.top - 30) + 'px';
        indicator.style.transform = 'translateX(-50%)';
        
        document.body.appendChild(indicator);
        
        // Auto-hide after 2 seconds
        setTimeout(() => {
            this.hidePositionIndicator();
        }, 2000);
    }
    
    hidePositionIndicator() {
        const indicator = document.querySelector('.kms-position-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    // Duplicate element
    duplicateElement(element) {
        const clone = element.cloneNode(true);
        clone.dataset.elementId = `${element.dataset.elementId}_copy_${Date.now()}`;
        
        // Offset position slightly
        const currentX = parseInt(element.style.left) || 0;
        const currentY = parseInt(element.style.top) || 0;
        clone.style.left = (currentX + 20) + 'px';
        clone.style.top = (currentY + 20) + 'px';
        
        // Remove selection class
        clone.classList.remove('selected');
        
        // Setup interactions for the clone
        this.posterMaker.setupElementInteraction(clone);
        
        // Add to canvas and elements array
        const canvas = document.getElementById('posterCanvas');
        canvas.appendChild(clone);
        this.posterMaker.elements.push(clone);
        
        // Select the new element
        this.posterMaker.selectElement(clone);
        
        return clone;
    }
    
    // Group elements
    groupElements(elements) {
        if (elements.length < 2) return null;
        
        const group = document.createElement('div');
        group.className = 'kms-canvas-element kms-group-element';
        group.dataset.elementId = `group_${++this.posterMaker.elementCounter}`;
        
        // Calculate bounding box
        let minX = Infinity, minY = Infinity, maxX = 0, maxY = 0;
        
        elements.forEach(element => {
            const x = parseInt(element.style.left) || 0;
            const y = parseInt(element.style.top) || 0;
            const rect = element.getBoundingClientRect();
            
            minX = Math.min(minX, x);
            minY = Math.min(minY, y);
            maxX = Math.max(maxX, x + rect.width);
            maxY = Math.max(maxY, y + rect.height);
        });
        
        // Set group position and size
        group.style.left = minX + 'px';
        group.style.top = minY + 'px';
        group.style.width = (maxX - minX) + 'px';
        group.style.height = (maxY - minY) + 'px';
        group.style.border = '2px dashed #667eea';
        group.style.background = 'rgba(102, 126, 234, 0.1)';
        
        // Move elements into group and adjust their positions
        elements.forEach(element => {
            const x = parseInt(element.style.left) || 0;
            const y = parseInt(element.style.top) || 0;
            element.style.left = (x - minX) + 'px';
            element.style.top = (y - minY) + 'px';
            group.appendChild(element);
        });
        
        // Setup interactions
        this.posterMaker.setupElementInteraction(group);
        
        // Add to canvas
        const canvas = document.getElementById('posterCanvas');
        canvas.appendChild(group);
        
        // Update elements array
        this.posterMaker.elements = this.posterMaker.elements.filter(el => !elements.includes(el));
        this.posterMaker.elements.push(group);
        
        return group;
    }
}

// Initialize drag drop handler when main app is ready
document.addEventListener('DOMContentLoaded', () => {
    if (window.kmsPosterMaker) {
        window.kmsPosterMaker.dragDropHandler = new KMSDragDropHandler(window.kmsPosterMaker);
    }
});
