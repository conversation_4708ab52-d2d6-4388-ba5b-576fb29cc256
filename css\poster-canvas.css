/* <PERSON><PERSON> Poster Maker - Canvas Styles */
/* 海報畫布樣式 */

.kms-canvas-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    position: relative;
    overflow: visible;
    min-height: calc(100vh - 90px);
    width: 100%;
    box-sizing: border-box;
}

.kms-poster-canvas {
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
    display: block;
    visibility: visible;
    width: 816px;  /* Default to letter size */
    height: 1056px;
    transform-origin: center center;
    flex-shrink: 0;
}

/* Paper Sizes */
.kms-canvas-letter {
    width: 816px !important;  /* 8.5 inches at 96 DPI */
    height: 1056px !important; /* 11 inches at 96 DPI */
}

.kms-canvas-4x6 {
    width: 384px !important;  /* 4 inches at 96 DPI */
    height: 576px !important;  /* 6 inches at 96 DPI */
}

/* Canvas Elements */
.kms-canvas-element {
    position: absolute;
    cursor: move;
    user-select: none;
    transition: transform 0.1s ease;
}

/* Locked Elements */
.kms-locked-element {
    cursor: not-allowed !important;
    opacity: 0.7;
    position: relative;
}

.kms-locked-element::after {
    content: '🔒';
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ffc107;
    color: #212529;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.kms-canvas-element:hover {
    z-index: 10;
}

.kms-canvas-element.selected {
    z-index: 20;
}

.kms-canvas-element.selected::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px dashed #667eea;
    border-radius: 4px;
    pointer-events: none;
}

/* Text Elements */
.kms-text-element {
    padding: 8px;
    min-width: 50px;
    min-height: 30px;
    word-wrap: break-word;
    white-space: pre-wrap;
    outline: none;
    resize: none;
    border: none;
    background: transparent;
    font-family: inherit;
    line-height: 1.4;
}

.kms-text-element:focus {
    background: rgba(102, 126, 234, 0.05);
}

/* Image Elements */
.kms-image-element {
    max-width: 100%;
    height: auto;
    border-radius: 0;
    object-fit: contain;
}

.kms-image-element.resizable {
    resize: both;
    overflow: hidden;
}

/* QR Code Elements */
.kms-qr-element {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.kms-qr-element img {
    display: block;
    width: 100%;
    height: auto;
}

/* Canvas Borders */
.kms-canvas-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    border-radius: 8px;
    box-sizing: border-box;
    z-index: 0; /* 確保邊框在內容下方但仍然可見 */
}

/* Premium Certificate Border - Multi-layered Gold Frame */
.kms-border-certificate {
    border: 30px solid #c8860d;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 */
        0 0 0 2px #f4e4bc,
        0 0 0 4px #b8860b,
        0 0 0 6px #f4e4bc,
        0 0 0 8px #c8860d,
        /* 內層邊框 */
        inset 0 0 0 3px #f4e4bc,
        inset 0 0 0 6px #c8860d,
        inset 0 0 0 9px #f4e4bc,
        inset 0 0 0 12px #b8860b,
        inset 0 0 0 15px #f4e4bc;
}

.kms-border-certificate::before {
    content: '';
    position: absolute;
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    background:
        linear-gradient(45deg, #8b6914 0%, #c8860d 25%, #f4e4bc 50%, #c8860d 75%, #8b6914 100%);
    z-index: 0;
    pointer-events: none;
}

.kms-border-certificate::after {
    content: '';
    position: absolute;
    top: 18px;
    left: 18px;
    right: 18px;
    bottom: 18px;
    border: 2px double #c8860d;
    z-index: 1;
    pointer-events: none;
}

/* Royal Award Border - Triple Gold Lines */
.kms-border-royal {
    border: 35px solid #b8860b;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 更寬更豪華 */
        0 0 0 3px #ffd700,
        0 0 0 6px #c8860d,
        0 0 0 9px #f4e4bc,
        0 0 0 12px #b8860b,
        0 0 0 15px #ffd700,
        /* 內層邊框 */
        inset 0 0 0 4px #ffd700,
        inset 0 0 0 8px #c8860d,
        inset 0 0 0 12px #f4e4bc,
        inset 0 0 0 16px #b8860b;
}

.kms-border-royal::before {
    content: '';
    position: absolute;
    top: -35px;
    left: -35px;
    right: -35px;
    bottom: -35px;
    background:
        radial-gradient(circle at center, #ffd700 0%, #d4af37 50%, #b8860b 100%);
    z-index: 0;
    pointer-events: none;
}

.kms-border-royal::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 3px solid #ffd700;
    background:
        repeating-linear-gradient(45deg,
            transparent 0px, transparent 15px,
            #ffd700 15px, #ffd700 17px,
            transparent 17px, transparent 32px
        );
    z-index: 1;
    pointer-events: none;
}



/* Ornate Award Border - Multi-line Gold Certificate */
.kms-border-ornate {
    border: 40px solid #ffd700;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 更多層次 */
        0 0 0 2px #c8860d,
        0 0 0 4px #f4e4bc,
        0 0 0 6px #ffd700,
        0 0 0 8px #c8860d,
        0 0 0 10px #f4e4bc,
        0 0 0 12px #b8860b,
        /* 內層邊框 */
        inset 0 0 0 2px #ffd700,
        inset 0 0 0 5px #c8860d,
        inset 0 0 0 8px #f4e4bc,
        inset 0 0 0 11px #ffd700,
        inset 0 0 0 14px #c8860d,
        inset 0 0 0 17px #f4e4bc,
        inset 0 0 0 20px #b8860b;
}

.kms-border-ornate::before {
    content: '';
    position: absolute;
    top: -40px;
    left: -40px;
    right: -40px;
    bottom: -40px;
    background:
        conic-gradient(from 0deg, #8b6914, #c8860d, #ffd700, #f4e4bc, #ffd700, #c8860d, #8b6914);
    z-index: 0;
    pointer-events: none;
}

.kms-border-ornate::after {
    content: '';
    position: absolute;
    top: 25px;
    left: 25px;
    right: 25px;
    bottom: 25px;
    border: 2px solid #ffd700;
    background:
        repeating-conic-gradient(from 0deg at center,
            transparent 0deg, transparent 30deg,
            #ffd700 30deg, #ffd700 35deg,
            transparent 35deg, transparent 65deg
        );
    z-index: 1;
    pointer-events: none;
}

/* Diploma Border */
.kms-border-diploma {
    border: 20px solid #2c3e50;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 學術風格 */
        0 0 0 3px #d4af37,
        0 0 0 6px #2c3e50,
        0 0 0 9px #f4e4bc,
        /* 內層邊框 */
        inset 0 0 0 3px #d4af37,
        inset 0 0 0 6px #2c3e50,
        inset 0 0 0 9px #f4e4bc,
        inset 0 0 0 12px #c8860d;
}

.kms-border-diploma::before {
    content: '';
    position: absolute;
    top: -29px;
    left: -29px;
    right: -29px;
    bottom: -29px;
    background:
        linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    z-index: 0;
    pointer-events: none;
}

.kms-border-diploma::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border: 3px double #d4af37;
    z-index: 1;
    pointer-events: none;
}

/* Vintage Certificate */
.kms-border-vintage-cert {
    border: 28px solid #8b4513;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 復古風格 */
        0 0 0 2px #daa520,
        0 0 0 4px #8b4513,
        0 0 0 6px #f4e4bc,
        0 0 0 8px #c8860d,
        /* 內層邊框 */
        inset 0 0 0 3px #daa520,
        inset 0 0 0 6px #8b4513,
        inset 0 0 0 9px #f4e4bc,
        inset 0 0 0 12px #c8860d;
}

.kms-border-vintage-cert::before {
    content: '';
    position: absolute;
    top: -36px;
    left: -36px;
    right: -36px;
    bottom: -36px;
    background:
        repeating-conic-gradient(from 0deg, #8b4513 0deg 45deg, #daa520 45deg 90deg);
    z-index: 0;
    pointer-events: none;
}

.kms-border-vintage-cert::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 2px solid #8b4513;
    background:
        repeating-linear-gradient(45deg,
            transparent 0px, transparent 10px,
            #daa520 10px, #daa520 12px,
            transparent 12px, transparent 22px
        );
    z-index: 1;
    pointer-events: none;
}

/* Art Deco Border */
.kms-border-artdeco {
    border: 25px solid #000;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - Art Deco風格 */
        0 0 0 3px #d4af37,
        0 0 0 6px #000,
        0 0 0 9px #ffd700,
        0 0 0 12px #000,
        /* 內層邊框 */
        inset 0 0 0 3px #d4af37,
        inset 0 0 0 6px #000,
        inset 0 0 0 9px #ffd700,
        inset 0 0 0 12px #000;
}

.kms-border-artdeco::before {
    content: '';
    position: absolute;
    top: -37px;
    left: -37px;
    right: -37px;
    bottom: -37px;
    background:
        linear-gradient(0deg, #d4af37, #ffd700);
    z-index: 0;
    pointer-events: none;
}

.kms-border-artdeco::after {
    content: '';
    position: absolute;
    top: 18px;
    left: 18px;
    right: 18px;
    bottom: 18px;
    border: 2px solid #000;
    background:
        linear-gradient(45deg, #000 25%, transparent 25%),
        linear-gradient(-45deg, #000 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #000 75%),
        linear-gradient(-45deg, transparent 75%, #000 75%);
    background-size: 20px 20px;
    z-index: 1;
    pointer-events: none;
}

/* Elegant Frame */
.kms-border-elegant-frame {
    border: 18px solid #2c3e50;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 優雅風格 */
        0 0 0 3px #d4af37,
        0 0 0 6px #2c3e50,
        0 0 20px rgba(0,0,0,0.3),
        /* 內層邊框 */
        inset 0 0 0 3px #d4af37,
        inset 0 0 0 6px #2c3e50;
}

.kms-border-elegant-frame::before {
    content: '';
    position: absolute;
    top: -27px;
    left: -27px;
    right: -27px;
    bottom: -27px;
    background:
        linear-gradient(45deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    z-index: 0;
    pointer-events: none;
}

.kms-border-elegant-frame::after {
    content: '';
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    bottom: 12px;
    border: 2px solid #d4af37;
    z-index: 1;
    pointer-events: none;
}

/* Classic Certificate */
.kms-border-classic-cert {
    border: 22px solid #8b0000;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 經典風格 */
        0 0 0 2px #ffd700,
        0 0 0 4px #8b0000,
        0 0 0 6px #ffd700,
        0 0 0 8px #8b0000,
        /* 內層邊框 */
        inset 0 0 0 3px #ffd700,
        inset 0 0 0 6px #8b0000,
        inset 0 0 0 9px #ffd700;
}

.kms-border-classic-cert::before {
    content: '';
    position: absolute;
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    background:
        repeating-linear-gradient(45deg,
            #8b0000 0px, #8b0000 10px,
            #ffd700 10px, #ffd700 15px,
            #8b0000 15px, #8b0000 25px
        );
    z-index: 0;
    pointer-events: none;
}

.kms-border-classic-cert::after {
    content: '';
    position: absolute;
    top: 16px;
    left: 16px;
    right: 16px;
    bottom: 16px;
    border: 2px solid #ffd700;
    z-index: 1;
    pointer-events: none;
}

/* Modern Certificate */
.kms-border-modern-cert {
    border: 16px solid transparent;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 現代風格 */
        0 0 0 2px #667eea,
        0 0 0 4px #764ba2,
        0 0 0 6px #667eea,
        0 0 0 8px #764ba2,
        /* 內層邊框 */
        inset 0 0 0 2px #667eea,
        inset 0 0 0 4px #764ba2,
        inset 0 0 0 6px #667eea,
        inset 0 0 0 8px #764ba2;
    border-image: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #667eea 50%, #764ba2 75%, #667eea 100%) 16;
}

.kms-border-modern-cert::before {
    content: '';
    position: absolute;
    top: -24px;
    left: -24px;
    right: -24px;
    bottom: -24px;
    background:
        linear-gradient(135deg, #667eea 0%, #764ba2 25%, #667eea 50%, #764ba2 75%, #667eea 100%);
    z-index: 0;
    border-radius: 4px;
    pointer-events: none;
}

.kms-border-modern-cert::after {
    content: '';
    position: absolute;
    top: 12px;
    left: 12px;
    right: 12px;
    bottom: 12px;
    border: 1px solid #667eea;
    border-radius: 8px;
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
    z-index: 1;
    pointer-events: none;
}

/* Premium Award Certificate - Multi-layered Gold Frame */
.kms-border-premium-award {
    border: 40px solid #ffd700;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框 - 最豪華的設計 */
        0 0 0 3px #c8860d,
        0 0 0 6px #ffd700,
        0 0 0 9px #f4e4bc,
        0 0 0 12px #c8860d,
        0 0 0 15px #ffd700,
        0 0 0 18px #b8860b,
        /* 內層邊框 */
        inset 0 0 0 2px #c8860d,
        inset 0 0 0 5px #ffd700,
        inset 0 0 0 8px #f4e4bc,
        inset 0 0 0 11px #c8860d,
        inset 0 0 0 14px #ffd700,
        inset 0 0 0 17px #f4e4bc,
        inset 0 0 0 20px #c8860d,
        inset 0 0 0 23px #ffd700,
        inset 0 0 0 26px #b8860b;
}

.kms-border-premium-award::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background:
        radial-gradient(circle at 40px 40px, #ffd700 0%, #d4af37 40%, transparent 60%),
        radial-gradient(circle at calc(100% - 40px) 40px, #ffd700 0%, #d4af37 40%, transparent 60%),
        radial-gradient(circle at 40px calc(100% - 40px), #ffd700 0%, #d4af37 40%, transparent 60%),
        radial-gradient(circle at calc(100% - 40px) calc(100% - 40px), #ffd700 0%, #d4af37 40%, transparent 60%),
        linear-gradient(135deg, #8b6914 0%, #b8860b 20%, #d4af37 40%, #ffd700 60%, #d4af37 80%, #8b6914 100%);
    z-index: 0;
    border-radius: 8px;
    pointer-events: none;
}

.kms-border-premium-award::after {
    content: '';
    position: absolute;
    top: 30px;
    left: 30px;
    right: 30px;
    bottom: 30px;
    border: 3px solid #ffd700;
    background:
        repeating-conic-gradient(from 0deg at center,
            transparent 0deg, transparent 20deg,
            #ffd700 20deg, #ffd700 25deg,
            transparent 25deg, transparent 45deg
        );
    z-index: 1;
    pointer-events: none;
}

/* Classic Multi-line Certificate Border - Exact Match */
.kms-border-classic-multilayer {
    border: 25px solid #d4af37;
    background: #faf6f0;
    position: relative;
    box-shadow:
        /* 外層邊框陰影 */
        0 0 0 3px #b8860b,
        0 0 0 6px #f4e4bc,
        0 0 0 9px #c8860d,
        0 0 0 12px #f4e4bc,
        0 0 0 15px #b8860b,
        /* 內層邊框 */
        inset 0 0 0 2px #c8860d,
        inset 0 0 0 4px #f4e4bc,
        inset 0 0 0 6px #c8860d,
        inset 0 0 0 8px #f4e4bc,
        inset 0 0 0 10px #c8860d,
        inset 0 0 0 12px #f4e4bc,
        inset 0 0 0 14px #c8860d;
}

.kms-border-classic-multilayer::before {
    content: '';
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    background:
        linear-gradient(135deg, #8b6914 0%, #d4af37 25%, #ffd700 50%, #d4af37 75%, #8b6914 100%);
    z-index: 0;
    pointer-events: none;
}

.kms-border-classic-multilayer::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border: 1px solid #c8860d;
    background:
        linear-gradient(45deg,
            transparent 0px, transparent 20px,
            #c8860d 20px, #c8860d 22px,
            transparent 22px, transparent 42px
        ),
        linear-gradient(-45deg,
            transparent 0px, transparent 20px,
            #c8860d 20px, #c8860d 22px,
            transparent 22px, transparent 42px
        );
    background-size: 42px 42px;
    z-index: 1;
    pointer-events: none;
}

/* Canvas Tools */
.kms-canvas-tools {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    gap: 0.5rem;
    z-index: 100;
}

.kms-canvas-tool {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.kms-canvas-tool:hover {
    background: #f8f9fa;
    transform: translateY(-1px);
}

.kms-canvas-tool.active {
    background: #667eea;
    color: white;
}

/* Resize Handles */
.kms-resize-handle {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #667eea;
    border: 1px solid white;
    border-radius: 50%;
    cursor: nw-resize;
}

.kms-resize-handle.nw { top: -4px; left: -4px; cursor: nw-resize; }
.kms-resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
.kms-resize-handle.sw { bottom: -4px; left: -4px; cursor: sw-resize; }
.kms-resize-handle.se { bottom: -4px; right: -4px; cursor: se-resize; }
.kms-resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); cursor: n-resize; }
.kms-resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: s-resize; }
.kms-resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); cursor: w-resize; }
.kms-resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); cursor: e-resize; }

/* Print Styles */
@media print {
    .kms-canvas-container {
        padding: 0;
        background: white;
    }
    
    .kms-poster-canvas {
        box-shadow: none;
        border-radius: 0;
        page-break-inside: avoid;
    }
    
    .kms-canvas-element.selected::after {
        display: none;
    }
    
    .kms-canvas-tools,
    .kms-resize-handle {
        display: none !important;
    }
}

/* Animation for canvas transitions */
@keyframes kms-canvas-appear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.kms-poster-canvas {
    animation: kms-canvas-appear 0.3s ease-out;
}

/* Grid overlay for alignment */
.kms-canvas-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    pointer-events: none;
    background-image: 
        linear-gradient(rgba(102, 126, 234, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(102, 126, 234, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    transition: opacity 0.3s ease;
}

.kms-canvas-grid.active {
    opacity: 1;
}

/* Canvas styles optimized for visibility and performance */