/* <PERSON><PERSON> Poster Maker - Controls Panel Styles */
/* 控制面板樣式 */

.kms-controls-panel {
    padding: 2rem;
    background: white;
    height: 100%;
    overflow-y: auto;
}

.kms-controls-section {
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1.5rem;
}

.kms-controls-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.kms-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-section-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background: #667eea;
    border-radius: 2px;
}

/* Paper Size Controls */
.kms-paper-size-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.kms-paper-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.kms-paper-option:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-paper-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.kms-paper-option .size-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.kms-paper-option .size-dimensions {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Text Controls */
.kms-text-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-font-selector {
    position: relative;
}

.kms-font-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    font-size: 1.1rem;
    text-align: center;
}

.kms-text-style-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 0.5rem;
}

.kms-style-btn {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.kms-style-btn:hover {
    background: #e9ecef;
}

.kms-style-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.kms-color-row {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.kms-color-preview {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    border: 2px solid #e9ecef;
    cursor: pointer;
    position: relative;
}

.kms-color-preview::after {
    content: attr(data-color);
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: #6c757d;
    white-space: nowrap;
}

/* Border Controls */
.kms-border-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-border-style-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.kms-border-style-option {
    aspect-ratio: 1;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.kms-border-style-option:hover {
    border-color: #667eea;
}

.kms-border-style-option.active {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-border-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    color: #6c757d;
}

/* Image Controls */
.kms-image-upload {
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 2rem 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.kms-image-upload:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-image-upload.dragover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: scale(1.02);
}

.kms-upload-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #6c757d;
}

.kms-upload-text {
    color: #6c757d;
    font-size: 0.9rem;
}

.kms-upload-text strong {
    color: #495057;
}

/* QR Code Controls */
.kms-qr-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-qr-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kms-qr-preview img {
    max-width: 100px;
    max-height: 100px;
}

.kms-qr-placeholder {
    color: #6c757d;
    font-style: italic;
}

/* Background Controls */
.kms-background-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.kms-background-options {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.kms-bg-option {
    aspect-ratio: 1;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-bg-option:hover {
    border-color: #667eea;
    transform: scale(1.05);
}

.kms-bg-option.active {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Canvas Border Controls */
.kms-canvas-border-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
}

.kms-canvas-border-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kms-canvas-border-option:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.kms-canvas-border-option.active {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.kms-border-name {
    font-size: 0.8rem;
    font-weight: 500;
}

/* Quick Colors Section - Enhanced */
.kms-quick-colors-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.kms-color-target-buttons {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.kms-color-target-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 0.75rem 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: #495057;
    position: relative;
    overflow: hidden;
}

.kms-color-target-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.kms-color-target-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.kms-color-target-btn:hover::before {
    left: 100%;
}

.kms-color-target-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.kms-color-target-btn i {
    font-size: 1.2rem;
}

.kms-color-palette {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
}

.kms-color-palette-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e9ecef;
}

.kms-current-target-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
}

.kms-current-target-name {
    color: #667eea;
    font-weight: 700;
}

.kms-quick-colors-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    margin-top: 0;
    max-height: 400px;
}

.kms-quick-color-btn {
    width: 35px;
    height: 35px;
    border: 2px solid transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.kms-quick-color-btn:hover {
    transform: scale(1.05);
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    z-index: 10;
}

.kms-quick-color-btn:active {
    transform: scale(1.02);
}

.kms-quick-color-btn.active {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
    transform: scale(1.1);
}

.kms-color-tooltip {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 20;
}

.kms-quick-color-btn:hover .kms-color-tooltip {
    opacity: 1;
}

/* Action Buttons */
.kms-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e9ecef;
}

.kms-print-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    font-size: 1.1rem;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.kms-print-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
}

/* Additional form controls */
.kms-border-controls-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.kms-form-label-small {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
    color: #495057;
    font-size: 0.8rem;
}

.kms-font-size-display {
    text-align: center;
    margin-top: 0.5rem;
}

.kms-full-width-btn {
    width: 100%;
}

.kms-hidden-section {
    display: none !important;
}

/* QR Code Controls - Same as KMS_Logo_Photo.app */
.kms-qr-url-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 1rem;
    background: #f8f9fa;
}

.kms-qr-url-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.kms-qr-controls-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.kms-qr-control-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
}

.kms-qr-label {
    font-weight: 500;
    color: #495057;
    font-size: 14px;
    min-width: 60px;
}

.kms-qr-size-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-qr-size-input {
    width: 80px;
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    background: white;
}

.kms-qr-size-input:focus {
    outline: none;
    border-color: #667eea;
}

.kms-qr-unit {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.kms-qr-color-picker {
    width: 120px;
    height: 40px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.kms-qr-color-picker:focus {
    outline: none;
    border-color: #667eea;
}

.kms-qr-generate-btn {
    width: 100%;
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.kms-qr-generate-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

.kms-qr-generate-btn:active {
    transform: translateY(0);
}

.kms-hidden-file-input {
    display: none !important;
}

/* Layer Controls */
.kms-layer-info {
    background: var(--color-1);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.kms-layer-current,
.kms-layer-total {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.kms-layer-current span:first-child,
.kms-layer-total span:first-child {
    font-size: 0.8rem;
    color: var(--text-color-2);
    opacity: 0.8;
}

.kms-layer-current span:last-child,
.kms-layer-total span:last-child {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color-2);
}

.kms-layer-controls {
    margin-bottom: 1.5rem;
}

.kms-layer-buttons-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.kms-layer-buttons-row:last-child {
    margin-bottom: 0;
}

.kms-layer-btn {
    background: var(--color-2);
    color: var(--text-color-1);
    border: 2px solid var(--border-color-2);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.kms-layer-btn:hover {
    background: var(--color-3);
    border-color: var(--border-color-3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.kms-layer-btn:active {
    transform: translateY(0);
}

.kms-layer-btn:disabled {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #e9ecef;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.kms-layer-btn i {
    font-size: 1rem;
}

.kms-layer-list {
    border: 2px solid var(--border-color-1);
    border-radius: 8px;
    overflow: hidden;
}

.kms-layer-list-header {
    background: var(--color-1);
    color: var(--text-color-2);
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
}

.kms-layer-list-container {
    max-height: 400px;
    overflow-y: auto;
    background: white;
}

.kms-layer-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.3s ease;
}

.kms-layer-item:last-child {
    border-bottom: none;
}

.kms-layer-item:hover {
    background: var(--color-5);
    color: var(--text-color-2);
}

.kms-layer-item.selected {
    background: var(--color-4);
    color: var(--text-color-2);
}

.kms-layer-item-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-layer-item-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.kms-layer-item-name {
    font-weight: 500;
    font-size: 0.9rem;
}

.kms-layer-item-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.kms-layer-item-index {
    background: var(--color-3);
    color: var(--text-color-1);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.kms-layer-item.selected .kms-layer-item-index {
    background: var(--text-color-2);
    color: var(--color-4);
}

.kms-layer-delete-btn {
    background: var(--color-4);
    color: var(--text-color-2);
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.7rem;
    opacity: 0.7;
}

.kms-layer-delete-btn:hover {
    background: var(--border-color-4);
    opacity: 1;
    transform: scale(1.1);
}

.kms-layer-item:hover .kms-layer-delete-btn {
    opacity: 1;
}

/* Layer Lock Button */
.kms-layer-lock-btn {
    background: var(--color-2);
    color: var(--text-color-1);
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.7rem;
    opacity: 0.7;
}

.kms-layer-lock-btn:hover {
    background: var(--border-color-2);
    opacity: 1;
    transform: scale(1.1);
}

.kms-layer-lock-btn.locked {
    background: #ffc107;
    color: #212529;
    opacity: 1;
}

.kms-layer-lock-btn.locked:hover {
    background: #e0a800;
}

.kms-layer-item:hover .kms-layer-lock-btn {
    opacity: 1;
}

.kms-layer-item.selected .kms-layer-delete-btn {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kms-controls-panel {
        padding: 1rem;
    }

    .kms-paper-size-options,
    .kms-canvas-border-options {
        grid-template-columns: 1fr;
    }

    .kms-background-options {
        grid-template-columns: repeat(3, 1fr);
    }

    .kms-qr-size-input {
        width: 60px;
    }

    .kms-qr-color-picker {
        width: 100px;
    }

    .kms-layer-info {
        flex-direction: column;
        gap: 1rem;
    }

    .kms-layer-buttons-row {
        grid-template-columns: 1fr;
    }

    .kms-layer-btn {
        padding: 1rem;
        font-size: 1rem;
    }
}

/* Enhanced Text Controls Section */
.kms-text-controls-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.kms-text-control-group {
    background: white;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #e9ecef;
}

.kms-text-control-group:last-child {
    margin-bottom: 0;
}

.kms-control-group-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.kms-control-group-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.8s ease;
}

.kms-control-group-header:hover::before {
    left: 100%;
}

.kms-control-group-header i {
    font-size: 1.1rem;
    opacity: 0.9;
}

.kms-control-group-content {
    padding: 1.25rem;
}

/* Enhanced Form Controls */
.kms-enhanced-select {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.kms-enhanced-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    background: white;
}

.kms-range-control {
    position: relative;
}

.kms-enhanced-range {
    width: 100%;
    height: 8px;
    border-radius: 4px;
    background: linear-gradient(90deg, #e9ecef 0%, #dee2e6 100%);
    outline: none;
    -webkit-appearance: none;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.kms-enhanced-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.kms-enhanced-range::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.6);
}

.kms-enhanced-range::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 3px 8px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.kms-range-display {
    text-align: center;
    margin-top: 0.5rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
}

.kms-range-display span {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

/* Enhanced Color Controls */
.kms-enhanced-color-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
}

.kms-enhanced-color-picker {
    width: 60px;
    height: 45px;
    border: 3px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    background: none;
    padding: 0;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.kms-enhanced-color-picker:hover {
    border-color: #667eea;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.kms-enhanced-color-preview {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    border: 3px solid #e9ecef;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.kms-enhanced-color-preview:hover {
    border-color: #667eea;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.kms-color-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    min-width: 80px;
    text-align: center;
}

/* Enhanced Style Buttons */
.kms-enhanced-style-group {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
}

.kms-enhanced-style-btn {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.875rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 0.8rem;
    color: #495057;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.kms-enhanced-style-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.kms-enhanced-style-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.kms-enhanced-style-btn:hover::before {
    left: 100%;
}

.kms-enhanced-style-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.kms-enhanced-style-btn i {
    font-size: 1.2rem;
}

/* Dual Range Controls */
.kms-dual-range-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.kms-dual-range-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #dee2e6;
}

/* Form Label Enhancements */
.kms-form-label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 700;
    color: #495057;
    font-size: 0.95rem;
    position: relative;
}

.kms-form-label::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

.kms-form-label-small {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.85rem;
    text-align: center;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .kms-color-target-buttons {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.25rem;
    }
    
    .kms-color-target-btn {
        padding: 0.5rem 0.25rem;
        font-size: 0.7rem;
    }
    
    .kms-quick-colors-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }
    
    .kms-quick-color-btn {
        width: 35px;
        height: 35px;
    }
    
    .kms-enhanced-style-group {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .kms-dual-range-controls {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .kms-enhanced-color-row {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
}

@media (max-width: 480px) {
    .kms-color-target-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .kms-quick-colors-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .kms-text-control-group {
        margin-bottom: 1rem;
    }
    
    .kms-control-group-content {
        padding: 1rem;
    }
}/* Animatio
n Keyframes */
@keyframes colorPulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Apply animations to control groups */
.kms-text-control-group {
    animation: fadeInUp 0.3s ease-out;
}

.kms-text-control-group:nth-child(2) { animation-delay: 0.1s; }
.kms-text-control-group:nth-child(3) { animation-delay: 0.2s; }
.kms-text-control-group:nth-child(4) { animation-delay: 0.3s; }
.kms-text-control-group:nth-child(5) { animation-delay: 0.4s; }

.kms-color-target-btn.active {
    animation: colorPulse 2s infinite;
}

/* Improved focus states */
.kms-enhanced-select:focus,
.kms-enhanced-color-picker:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Loading states */
.kms-control-group-header.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-left: auto;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility improvements */
.kms-enhanced-style-btn:focus,
.kms-color-target-btn:focus,
.kms-quick-color-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .kms-text-control-group,
    .kms-quick-colors-section {
        border: 2px solid;
    }
    
    .kms-enhanced-style-btn,
    .kms-color-target-btn {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .kms-text-control-group,
    .kms-enhanced-style-btn,
    .kms-color-target-btn,
    .kms-quick-color-btn {
        animation: none;
        transition: none;
    }
    
    .kms-enhanced-style-btn:hover,
    .kms-color-target-btn:hover,
    .kms-quick-color-btn:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .kms-quick-colors-section,
    .kms-text-controls-section {
        display: none !important;
    }
}